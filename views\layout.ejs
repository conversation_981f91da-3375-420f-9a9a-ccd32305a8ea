<!DOCTYPE html>
<html lang="<%= locale || 'id' %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %> - StreamOnPod
  </title>

  <!-- SEO Meta Tags -->
  <% if (typeof seo !== 'undefined' && seo) { %>
    <%- seo.renderMetaTags() %>
  <% } %>

  <!-- Favicon -->
  <link rel="icon" href="/images/streamonpod-logo.png" type="image/png">
  <link rel="apple-touch-icon" href="/images/streamonpod-logo.png">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.30.0/tabler-icons.min.css">
  <!-- Critical CSS inline for faster loading -->
  <link rel="stylesheet" href="/css/critical.css" inline>
  <!-- Preload main stylesheet -->
  <link rel="preload" href="/css/styles.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="/css/styles.min.css"></noscript>
  <!-- Modern Notification System -->
  <link rel="stylesheet" href="/css/notifications.css">

  <link href="https://vjs.zencdn.net/7.20.3/video-js.css" rel="stylesheet" />
  <script src="https://vjs.zencdn.net/7.20.3/video.min.js"></script>
  <script>
    // Suppress Tailwind production warning
    if (typeof console !== 'undefined' && console.warn) {
      const originalWarn = console.warn;
      console.warn = function(...args) {
        if (args[0] && args[0].includes && args[0].includes('cdn.tailwindcss.com should not be used in production')) {
          return; // Suppress this specific warning
        }
        originalWarn.apply(console, args);
      };
    }

    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'inter': ['Inter', 'sans-serif'],
          'sans': ['Inter', 'system-ui', 'sans-serif']
        },
        extend: {
          colors: {
            'primary': '#ad6610',
            'secondary': '#8b5209',
            'dark': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '300': '#AFAFAF',
              '200': '#CFCFCF',
              '100': '#E5E5E5',
            },
            'gray': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '300': '#AFAFAF',
              '200': '#CFCFCF',
              '100': '#E5E5E5',
              '50': '#F5F5F5',
            }
          }
        }
      }
    }
  </script>
  <!-- Load optimized JavaScript -->
  <script src="/js/stream-modal.min.js" defer></script>
  <script src="/js/lazy-loading.js" defer></script>
  <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
  <script src="/socket.io/socket.io.js"></script>
  <% } %>

  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            // Service worker registered successfully (logging removed for production)
          })
          .catch(registrationError => {
            // Service worker registration failed (logging removed for production)
          });
      });
    }
  </script>

  <!-- JSON-LD Structured Data -->
  <% if (typeof seo !== 'undefined' && seo && seo.renderJsonLd) { %>
    <%- seo.renderJsonLd() %>
  <% } %>
</head>
<body class="bg-dark-900 text-white font-inter">
  <div
    class="lg:hidden fixed top-0 left-0 right-0 h-16 bg-dark-800 shadow-lg flex items-center justify-between px-4 z-30">
    <div class="flex items-center">
      <picture>
        <source srcset="/images/streamonpod-logotype-sm.webp" type="image/webp">
        <img data-src="/images/streamonpod-logotype-sm.png" alt="StreamOnPod Logo" class="h-8 lazy">
      </picture>
    </div>
    <div class="flex items-center gap-1">
      <a href="https://t.me/streamonpod_support" target="_blank"
         class="p-2 text-gray-400 hover:text-white transition-colors">
         <i class="ti ti-brand-telegram text-lg"></i>
      </a>

      <!-- Mobile Language Switcher -->
      <div class="relative">
        <button id="mobile-language-btn" class="p-2 text-gray-400 hover:text-white transition-colors relative">
          <i class="ti ti-world text-lg"></i>
        </button>
      </div>

      <div class="relative">
        <button id="mobile-notification-btn" class="p-2 text-gray-400 hover:text-white transition-colors relative">
          <i class="ti ti-bell-ringing text-lg"></i>
          <span class="hidden absolute top-0.5 right-0.5 w-2 h-2 bg-red-500 rounded-full"></span>
        </button>
      </div>
    </div>
  </div>
  <!-- Desktop Header Navigation -->
  <div class="hidden lg:block fixed top-0 left-0 right-0 h-20 bg-dark-800 shadow-lg z-30 border-b border-gray-700">
    <div class="flex items-center justify-between px-8 h-full max-w-screen-2xl mx-auto">
      <!-- Logo -->
      <div class="flex items-center flex-shrink-0">
        <picture>
          <source srcset="/images/streamonpod-logotype-sm.webp" type="image/webp">
          <img data-src="/images/streamonpod-logotype-sm.png" alt="StreamOnPod Logo" class="h-16 lazy header-logo">
        </picture>
      </div>

      <!-- Navigation Menu -->
      <div class="flex items-center space-x-4 flex-1 justify-center">
        <a href="/dashboard" class="header-nav-item <%= active === 'dashboard' ? 'header-nav-active' : '' %>">
          <i class="ti ti-device-tv text-lg"></i>
          <span><%= t('common.streams') %></span>
        </a>
        <a href="/gallery" class="header-nav-item <%= active === 'gallery' ? 'header-nav-active' : '' %>">
          <i class="ti ti-photo-video text-lg"></i>
          <span><%= t('common.gallery') %></span>
        </a>
        <a href="/history" class="header-nav-item <%= active === 'history' ? 'header-nav-active' : '' %>">
          <i class="ti ti-clock-history text-lg"></i>
          <span><%= t('common.history') %></span>
        </a>
        <a href="/subscription/plans" class="header-nav-item <%= active === 'subscription' ? 'header-nav-active' : '' %>">
          <i class="ti ti-crown text-lg"></i>
          <span><%= t('common.plans') %></span>
        </a>
        <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
        <a href="/admin/dashboard" class="header-nav-item <%= active === 'admin' ? 'header-nav-active' : '' %>">
          <i class="ti ti-settings-cog text-lg"></i>
          <span><%= t('common.admin') %></span>
        </a>
        <% } %>
      </div>

      <!-- Right Side Actions -->
      <div class="flex items-center space-x-4 flex-shrink-0">
        <a href="https://t.me/streamonpod_support" target="_blank"
           class="header-action-btn">
           <i class="ti ti-brand-telegram text-lg"></i>
        </a>

        <!-- Language Switcher -->
        <div class="relative">
          <button id="language-btn" class="header-action-btn flex items-center gap-2 group">
            <i class="ti ti-world text-lg group-hover:rotate-12 transition-transform duration-300"></i>
            <span class="text-xs font-semibold"><%= getCurrentLanguage().code.toUpperCase() %></span>
            <i class="ti ti-chevron-down text-xs transition-transform duration-300 group-hover:rotate-180"></i>
          </button>
          <div id="language-dropdown" class="hidden absolute right-0 top-full mt-2 w-52 bg-dark-800 border border-gray-700 rounded-xl shadow-2xl z-[9999] overflow-hidden">
            <div class="p-2">
              <div class="text-xs font-medium text-gray-500 px-3 py-2 border-b border-gray-700/50 mb-1">
                <%= t('common.language') %>
              </div>
              <% getLanguages().forEach(function(lang) { %>
              <a href="<%= getLanguageUrl(lang.code) %>"
                 class="group flex items-center px-3 py-2.5 text-sm text-gray-300 hover:bg-primary/20 hover:text-white transition-all duration-200 rounded-lg <%= locale === lang.code ? 'bg-primary/30 text-primary border border-primary/30' : '' %>">
                <i class="ti ti-world text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <div class="flex-1">
                  <div class="font-medium"><%= lang.native %></div>
                  <div class="text-xs text-gray-500 group-hover:text-gray-400"><%= lang.name %></div>
                </div>
                <% if (locale === lang.code) { %>
                <i class="ti ti-check text-primary ml-auto animate-pulse"></i>
                <% } %>
              </a>
              <% }); %>
            </div>
          </div>
        </div>

        <!-- Notifications -->
        <div class="relative">
          <button id="desktop-notification-btn" class="header-action-btn relative">
            <i class="ti ti-bell-ringing text-lg"></i>
            <span id="desktop-notification-badge" class="hidden absolute top-0.5 right-0.5 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <div id="desktop-notification-dropdown"
            class="hidden absolute right-0 top-full mt-2 w-80 bg-dark-800 border border-gray-700 rounded-lg shadow-xl z-[9999] max-h-80 overflow-y-auto">
            <div class="p-3 border-b border-gray-700 flex items-center justify-between">
              <h3 class="font-medium">Notifications</h3>
              <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
              <div class="flex items-center space-x-2">
                <button id="desktop-mark-all-read-btn" class="text-xs text-primary hover:underline">Mark all as read</button>
                <a href="/admin/notifications" class="text-xs text-blue-400 hover:underline">View all</a>
              </div>
              <% } else { %>
              <button id="desktop-mark-all-read-btn" class="text-xs text-primary hover:underline">Mark all as read</button>
              <% } %>
            </div>
            <div class="max-h-80 overflow-y-auto" id="desktop-notification-list">
              <div id="desktop-notification-loading" class="px-4 py-8 text-center text-gray-400">
                <i class="ti ti-loader-2 animate-spin text-lg mb-2"></i>
                <p class="text-sm">Loading notifications...</p>
              </div>
              <div id="desktop-notification-empty" class="px-4 py-8 text-center text-gray-400 hidden">
                <i class="ti ti-bell-off text-2xl mb-2"></i>
                <p class="text-sm">No notifications</p>
              </div>
            </div>
            <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
            <a href="/admin/notifications" class="block text-center p-2 text-sm text-primary hover:underline border-t border-gray-700">
              View all notifications
            </a>
            <% } %>
          </div>
        </div>

        <!-- Profile -->
        <div class="relative">
          <button id="desktop-profile-btn" class="header-action-btn flex items-center space-x-2">
            <div class="w-6 h-6 rounded-full overflow-hidden">
              <%- helpers.getAvatar(req) %>
            </div>
            <span class="text-sm font-medium"><%= helpers.getUsername(req) %></span>
            <i class="ti ti-chevron-down text-sm transition-transform duration-300" id="profile-chevron"></i>
          </button>
          <div id="desktop-profile-dropdown" class="hidden absolute right-0 top-full mt-2 w-48 bg-dark-800 border border-gray-700 rounded-lg shadow-xl z-[9999]">
            <div class="px-4 py-2 border-b border-gray-700">
              <div class="font-medium">
                <%= helpers.getUsername(req) %>
              </div>
            </div>
            <a href="/settings"
              class="flex items-center px-4 py-2.5 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors">
              <i class="ti ti-adjustments-horizontal mr-3"></i>
              <span><%= t('common.settings') %></span>
            </a>
            <a href="https://t.me/streamonpod_support"
              class="flex items-center px-4 py-2.5 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors" target="_blank">
              <i class="ti ti-brand-telegram mr-3"></i>
              <span><%= t('common.help') %></span>
            </a>
            <div class="h-px bg-gray-700 mx-4"></div>
            <a href="/logout" class="flex items-center px-4 py-2.5 text-sm text-logout hover:bg-dark-700 transition-colors">
              <i class="ti ti-logout mr-3"></i>
              <span><%= t('common.logout') %></span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="flex">
    <div class="w-full flex flex-col min-h-screen">
      <!-- Page Header with Breadcrumb -->
      <div class="hidden lg:block bg-dark-900 border-b border-gray-700 pt-20 pb-4 px-6">
        <div class="max-w-screen-2xl mx-auto">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="page-indicator">
                <%
                let pageTitle = '';
                let pageIcon = '';
                let pageDescription = '';

                switch(active) {
                  case 'dashboard':
                    pageTitle = t('dashboard.title');
                    pageIcon = 'ti-device-tv';
                    pageDescription = t('dashboard.subtitle') || 'Manage your live streams';
                    break;
                  case 'gallery':
                    pageTitle = t('gallery.title');
                    pageIcon = 'ti-photo-video';
                    pageDescription = 'Browse your video gallery';
                    break;
                  case 'history':
                    pageTitle = t('history.title');
                    pageIcon = 'ti-clock-history';
                    pageDescription = 'View your streaming history';
                    break;
                  case 'subscription':
                    pageTitle = t('subscription.title');
                    pageIcon = 'ti-crown';
                    pageDescription = 'Manage your subscription plans';
                    break;
                  case 'admin':
                    pageTitle = t('admin.title');
                    pageIcon = 'ti-settings-cog';
                    pageDescription = 'System administration panel';
                    break;
                  case 'settings':
                    pageTitle = t('common.settings');
                    pageIcon = 'ti-adjustments-horizontal';
                    pageDescription = 'Configure your preferences';
                    break;
                  default:
                    pageTitle = 'StreamOnPod';
                    pageIcon = 'ti-home-2';
                    pageDescription = 'Welcome to StreamOnPod';
                }
                %>
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center">
                    <i class="ti <%= pageIcon %> text-primary text-xl"></i>
                  </div>
                  <div>
                    <h1 class="text-xl font-bold text-white"><%= pageTitle %></h1>
                    <p class="text-sm text-gray-400"><%= pageDescription %></p>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-2 text-sm text-gray-400">
              <i class="ti ti-clock text-xs"></i>
              <span id="current-time"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="p-6 pt-6 lg:pt-6 flex-1">
        <%- body %>
      </div>

      <!-- Footer -->
      <footer class="bg-dark-800 border-t border-gray-700 py-4 px-6 mt-auto">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
          <div class="flex items-center gap-2">
            <div class="text-xs text-gray-500">
              StreamOnPod <span class="text-xs font-medium bg-gray-700 px-1 rounded">© 2025</span>
            </div>
            <div class="h-3 w-px bg-gray-700"></div>
            <a href="https://t.me/streamonpod_support" target="_blank" class="text-xs text-gray-500 hover:underline">
              <%= t('common.support') %></a>
            <div class="h-3 w-px bg-gray-700"></div>
            <a href="/tos" class="text-xs text-gray-500 hover:underline">
              <%= t('landing.footer.terms_of_service') %></a>
            <div class="h-3 w-px bg-gray-700"></div>
            <a href="/privacy-policy" class="text-xs text-gray-500 hover:underline">
              <%= t('landing.footer.privacy_policy') %></a>
          </div>
          <div class="flex items-center gap-2">
            <a href="https://t.me/streamonpod_support" target="_blank" class="kirim-tip-button relative">
              <span class="relative z-10 flex items-center gap-1 text-white font-medium px-1.5 py-0.5 text-xs">
                <i class="ti ti-brand-telegram text-white"></i>
                <span><%= t('common.support') %></span>
              </span>
              <div
                class="kirim-tip-tooltip px-3 py-2 bg-white text-gray-800 text-xs rounded-lg shadow-lg whitespace-nowrap">
                Need help? Contact us on Telegram 💬
                <div class="tooltip-arrow"></div>
              </div>
            </a>
          </div>
        </div>
      </footer>
    </div>
  </div>
  <div class="lg:hidden fixed bottom-0 left-0 right-0 bg-dark-800 border-t border-gray-700 shadow-lg z-30">
    <nav class="flex justify-around items-center h-16">
      <a href="/dashboard" class="bottom-nav-item <%= active === 'dashboard' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-device-tv"></i>
        <span><%= t('common.streams') %></span>
      </a>
      <a href="/gallery" class="bottom-nav-item <%= active === 'gallery' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-photo-video"></i>
        <span><%= t('common.gallery') %></span>
      </a>
      <a href="/history" class="bottom-nav-item <%= active === 'history' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-clock-history"></i>
        <span><%= t('common.history') %></span>
      </a>
      <a href="/subscription/plans" class="bottom-nav-item <%= active === 'subscription' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-crown"></i>
        <span><%= t('common.plans') %></span>
      </a>
      <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
      <a href="/admin/dashboard" class="bottom-nav-item <%= active === 'admin' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-settings-cog"></i>
        <span><%= t('common.admin') %></span>
      </a>
      <% } %>
      <a href="/settings" class="bottom-nav-item <%= active === 'settings' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-adjustments-horizontal"></i>
        <span><%= t('common.settings') %></span>
      </a>
      <button id="mobile-profile-btn" class="bottom-nav-item">
        <div class="relative">
          <div class="w-6 h-6 rounded-full overflow-hidden mx-auto">
            <%- helpers.getAvatar(req) %>
          </div>
        </div>
        <span><%= t('common.profile') %></span>
      </button>
    </nav>
    <div id="mobile-profile-popup"
      class="fixed bottom-16 right-2 bg-dark-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden hidden transform transition-all duration-200 ease-out opacity-0 translate-y-4 w-40">
      <div class="py-2 px-3">
        <div class="text-sm font-medium mb-1">
          <%= helpers.getUsername(req) %>
        </div>
        <div class="text-xs text-gray-400 mb-2">
          <%= req.session.email || '' %>
        </div>
        <div class="h-px bg-gray-700 my-2"></div>
        <a href="/logout" class="flex items-center py-2 text-red-400 hover:text-red-300 text-sm">
          <i class="ti ti-logout mr-2"></i>
          <span><%= t('common.logout') %></span>
        </a>
      </div>
    </div>
  </div>
  <div id="mobile-notification-popup"
    class="fixed bottom-16 right-2 bg-dark-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden hidden transform transition-all duration-200 ease-out opacity-0 translate-y-4 w-72">
    <div class="p-3 border-b border-gray-700 flex items-center justify-between">
      <h3 class="font-medium"><%= t('common.notifications') %></h3>
      <button class="text-xs text-primary hover:underline"><%= t('notifications.mark_all_read') %></button>
    </div>
    <div class="max-h-80 overflow-y-auto" id="mobile-notification-list">
      <div class="px-4 py-3 border-b border-gray-700 hover:bg-dark-700 transition-colors cursor-pointer" onclick="openStreamonpodUpdateModal()">
        <div class="flex items-start">
          <div class="shrink-0 mr-3">
            <div class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
              <i class="ti ti-info-circle text-primary"></i>
            </div>
          </div>
          <div>
            <p class="text-sm font-medium">StreamOnPod v2 Released!</p>
            <p class="text-sm text-gray-300 mt-1">Fitur dan improvisasi yang ada di StreamOnPod v2.</p>
            <p class="text-xs text-gray-400 mt-2">Baru saja</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Language Popup -->
  <div id="mobile-language-popup"
    class="fixed bottom-16 right-2 bg-dark-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden hidden transform transition-all duration-200 ease-out opacity-0 translate-y-4 w-48">
    <div class="p-3 border-b border-gray-700">
      <h3 class="font-medium text-sm"><%= t('common.language') %></h3>
    </div>
    <div class="p-2">
      <% getLanguages().forEach(function(lang) { %>
      <a href="<%= getLanguageUrl(lang.code) %>"
         class="flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors rounded <%= locale === lang.code ? 'bg-primary/20 text-primary' : '' %>">
        <span class="w-6 text-center mr-3"><%= lang.code.toUpperCase() %></span>
        <span><%= lang.native %></span>
        <% if (locale === lang.code) { %>
        <i class="ti ti-check ml-auto text-primary"></i>
        <% } %>
      </a>
      <% }); %>
    </div>
  </div>


</body>
</html>
<style>
  #profile-dropdown {
    position: fixed;
    z-index: 100;
    min-width: 220px;
    border-radius: 8px;
    transition: all 0.2s ease;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
    width: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    bottom: 80px;
    left: 15px;
  }
  #profile-dropdown.hidden {
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
    display: block !important;
    visibility: hidden;
  }
  @media (max-width: 768px) {
    #profile-dropdown {
      min-width: 200px;
      right: 20px;
      left: auto;
      bottom: auto;
      top: 100px;
    }
  }
  .text-logout {
    color: #FF5555;
  }
  .text-logout:hover {
    color: #FF7777;
  }
  .kirim-tip-button {
    position: relative;
    border-radius: 5px;
    background: linear-gradient(45deg, #01935d, #01e304);
    transition: all 0.3s ease;
  }
  .kirim-tip-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 168, 107, 0.3);
  }
  .kirim-tip-tooltip {
    position: absolute;
    bottom: 100%;
    right: 0;
    margin-bottom: 10px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
    transform: translateY(5px);
    z-index: 50;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
  }
  .kirim-tip-button:hover .kirim-tip-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  .tooltip-arrow {
    position: absolute;
    bottom: -6px;
    right: 10px;
    width: 12px;
    height: 12px;
    background-color: white;
    transform: rotate(45deg);
  }
  @media (max-width: 1023px) {
    body {
      padding-top: 64px; /* Account for mobile header height (h-16 = 64px) */
    }
  }
  .bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    padding: 8px 0;
    color: #8F8F8F;
    position: relative;
    transition: color 0.2s ease;
    min-width: 0; /* Allow flex items to shrink */
  }
  .bottom-nav-item i {
    font-size: 22px;
    margin-bottom: 4px;
  }
  .bottom-nav-item span {
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }
  .bottom-nav-item:hover {
    color: #E5E5E5;
  }
  .bottom-nav-active {
    color: #ad6610;
  }
  .bottom-nav-active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background-color: #ad6610;
    border-radius: 0 0 4px 4px;
  }

  /* Responsive bottom navigation (6-7 items) */
  @media (max-width: 480px) {
    .bottom-nav-item span {
      font-size: 9px;
    }
    .bottom-nav-item i {
      font-size: 18px;
      margin-bottom: 2px;
    }
    .bottom-nav-item {
      padding: 6px 1px;
    }
  }

  /* Extra small screens - further optimize for 7 items */
  @media (max-width: 360px) {
    .bottom-nav-item span {
      font-size: 8px;
    }
    .bottom-nav-item i {
      font-size: 16px;
      margin-bottom: 1px;
    }
    .bottom-nav-item {
      padding: 4px 0.5px;
    }
  }
  @media (max-width: 1023px) {
    .min-h-screen {
      padding-bottom: 80px !important;
    }
    #mobile-profile-popup.show {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
    }
  }
  #mobile-notification-popup.show {
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
  }
  #mobile-notification-popup {
    z-index: 100;
    transition: opacity 0.3s, transform 0.3s, visibility 0.3s;
    display: block !important;
    visibility: hidden;
  }
  #mobile-notification-popup.hidden {
    display: none !important;
  }

  /* Language popup styles */
  #mobile-language-popup.show {
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
  }
  #mobile-language-popup {
    z-index: 100;
    transition: opacity 0.3s, transform 0.3s, visibility 0.3s;
    display: block !important;
    visibility: hidden;
  }
  #mobile-language-popup.hidden {
    display: none !important;
  }
</style>
<script>
  document.addEventListener('DOMContentLoaded', () => {
    const profileBtn = document.getElementById('mobile-profile-btn');
    const profilePopup = document.getElementById('mobile-profile-popup');
    if (profileBtn && profilePopup) {
      profileBtn.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        if (profilePopup.classList.contains('hidden')) {
          profilePopup.classList.remove('hidden');
          setTimeout(() => {
            profilePopup.classList.add('show');
          }, 10);
        } else {
          profilePopup.classList.remove('show');
          setTimeout(() => {
            profilePopup.classList.add('hidden');
          }, 200);
        }
      });
      document.addEventListener('click', function (e) {
        if (!profilePopup.classList.contains('hidden') &&
          !profileBtn.contains(e.target) &&
          !profilePopup.contains(e.target)) {
          profilePopup.classList.remove('show');
          setTimeout(() => {
            profilePopup.classList.add('hidden');
          }, 200);
        }
      });
    }

    // Enhanced Language Switcher with Smooth Animations
    const languageBtn = document.getElementById('language-btn');
    const languageDropdown = document.getElementById('language-dropdown');

    if (languageBtn && languageDropdown) {
      languageBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isHidden = languageDropdown.classList.contains('hidden');

        // Close other dropdowns first
        const desktopProfileDropdown = document.getElementById('desktop-profile-dropdown');
        const profileChevron = document.getElementById('profile-chevron');
        if (desktopProfileDropdown && !desktopProfileDropdown.classList.contains('hidden')) {
          desktopProfileDropdown.classList.remove('show');
          setTimeout(() => {
            desktopProfileDropdown.classList.add('hidden');
          }, 200);
          if (profileChevron) {
            profileChevron.style.transform = 'rotate(0deg)';
          }
        }

        // Toggle language dropdown with animation
        if (isHidden) {
          languageDropdown.classList.remove('hidden');
          languageDropdown.style.display = 'block';
          languageDropdown.style.opacity = '0';
          languageDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            languageDropdown.style.opacity = '1';
            languageDropdown.style.transform = 'translateY(0) scale(1)';
          }, 10);
        } else {
          languageDropdown.style.opacity = '0';
          languageDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            languageDropdown.classList.add('hidden');
            languageDropdown.style.display = 'none';
          }, 200);
        }

        // Update aria-expanded for accessibility
        languageBtn.setAttribute('aria-expanded', isHidden);
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        const isClickInsideDropdown = languageDropdown.contains(e.target);
        const isClickOnButton = languageBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !languageDropdown.classList.contains('hidden')) {
          languageDropdown.style.opacity = '0';
          languageDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            languageDropdown.classList.add('hidden');
            languageDropdown.style.display = 'none';
          }, 200);
          languageBtn.setAttribute('aria-expanded', 'false');
        }
      });

      languageDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Mobile language switcher
    const mobileLanguageBtn = document.getElementById('mobile-language-btn');
    const mobileLanguagePopup = document.getElementById('mobile-language-popup');
    if (mobileLanguageBtn && mobileLanguagePopup) {
      mobileLanguageBtn.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        if (mobileLanguagePopup.classList.contains('hidden')) {
          mobileLanguagePopup.classList.remove('hidden');
          setTimeout(() => {
            mobileLanguagePopup.classList.add('show');
          }, 10);
        } else {
          mobileLanguagePopup.classList.remove('show');
          setTimeout(() => {
            mobileLanguagePopup.classList.add('hidden');
          }, 200);
        }
      });

      document.addEventListener('click', function (e) {
        if (!mobileLanguagePopup.classList.contains('hidden') &&
          !mobileLanguageBtn.contains(e.target) &&
          !mobileLanguagePopup.contains(e.target)) {
          mobileLanguagePopup.classList.remove('show');
          setTimeout(() => {
            mobileLanguagePopup.classList.add('hidden');
          }, 200);
        }
      });
    }

    // Enhanced Language Switcher with Animations
    // Note: Language switcher functionality is already handled above with the correct IDs

    const desktopNotificationBtn = document.getElementById('desktop-notification-btn');
    const desktopNotificationDropdown = document.getElementById('desktop-notification-dropdown');
    if (desktopNotificationBtn && desktopNotificationDropdown) {
      desktopNotificationBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        desktopNotificationDropdown.classList.toggle('hidden');
        if (!desktopNotificationDropdown.classList.contains('hidden')) {
          loadDesktopNotifications();
        }
      });

      document.addEventListener('click', (e) => {
        const isClickInsideDropdown = desktopNotificationDropdown.contains(e.target);
        const isClickOnButton = desktopNotificationBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !desktopNotificationDropdown.classList.contains('hidden')) {
          desktopNotificationDropdown.classList.add('hidden');
        }
      });

      desktopNotificationDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Enhanced Desktop Profile Dropdown with Smooth Animations
    const desktopProfileBtn = document.getElementById('desktop-profile-btn');
    const desktopProfileDropdown = document.getElementById('desktop-profile-dropdown');
    const profileChevron = document.getElementById('profile-chevron');

    if (desktopProfileBtn && desktopProfileDropdown) {
      desktopProfileBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isHidden = desktopProfileDropdown.classList.contains('hidden');

        // Close other dropdowns first
        const languageDropdown = document.getElementById('language-dropdown');
        if (languageDropdown && !languageDropdown.classList.contains('hidden')) {
          languageDropdown.classList.remove('opacity-100', 'translate-y-0', 'scale-100');
          languageDropdown.classList.add('opacity-0', 'translate-y-2', 'scale-95');
          setTimeout(() => {
            languageDropdown.classList.add('hidden');
          }, 200);
        }

        // Toggle profile dropdown with smooth animation
        if (isHidden) {
          desktopProfileDropdown.classList.remove('hidden');
          desktopProfileDropdown.style.display = 'block';
          setTimeout(() => {
            desktopProfileDropdown.classList.add('show');
          }, 10);
        } else {
          desktopProfileDropdown.classList.remove('show');
          setTimeout(() => {
            desktopProfileDropdown.classList.add('hidden');
          }, 200);
        }

        // Update aria-expanded for accessibility
        desktopProfileBtn.setAttribute('aria-expanded', isHidden);

        // Animate chevron with smooth rotation
        if (profileChevron) {
          profileChevron.style.transform = isHidden ? 'rotate(180deg)' : 'rotate(0deg)';
        }
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        const isClickInsideDropdown = desktopProfileDropdown.contains(e.target);
        const isClickOnButton = desktopProfileBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !desktopProfileDropdown.classList.contains('hidden')) {
          desktopProfileDropdown.classList.remove('show');
          setTimeout(() => {
            desktopProfileDropdown.classList.add('hidden');
          }, 200);
          desktopProfileBtn.setAttribute('aria-expanded', 'false');
          if (profileChevron) {
            profileChevron.style.transform = 'rotate(0deg)';
          }
        }
      });

      // Prevent dropdown from closing when clicking inside
      desktopProfileDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Update current time
    function updateTime() {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      const timeElement = document.getElementById('current-time');
      if (timeElement) {
        timeElement.textContent = timeString;
      }
    }

    // Update time immediately and then every minute
    updateTime();
    setInterval(updateTime, 60000);

    // Desktop mark all read button
    const desktopMarkAllReadBtn = document.getElementById('desktop-mark-all-read-btn');
    if (desktopMarkAllReadBtn) {
      desktopMarkAllReadBtn.addEventListener('click', markAllNotificationsAsRead);
    }

    // Initialize notification system
    initializeNotificationSystem();

    const notificationBtn = document.getElementById('notification-btn');
    const notificationDropdown = document.getElementById('notification-dropdown');
    // Debug logging removed for production

    if (notificationBtn && notificationDropdown) {
      notificationBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        // console.log('🔔 Notification button clicked!'); // Removed for production
        notificationDropdown.classList.toggle('hidden');
        console.log('🔔 Dropdown hidden class:', notificationDropdown.classList.contains('hidden'));
        if (!notificationDropdown.classList.contains('hidden')) {
          loadNotifications();
        }
      });
      document.addEventListener('click', (e) => {
        const isClickInsideDropdown = notificationDropdown.contains(e.target);
        const isClickOnButton = notificationBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !notificationDropdown.classList.contains('hidden')) {
          notificationDropdown.classList.add('hidden');
        }
      });
      notificationDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }
    const mobileNotificationBtn = document.getElementById('mobile-notification-btn');
    const mobileNotificationPopup = document.getElementById('mobile-notification-popup');
    if (mobileNotificationBtn && mobileNotificationPopup) {
      mobileNotificationBtn.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        const btnRect = mobileNotificationBtn.getBoundingClientRect();
        mobileNotificationPopup.style.top = '64px';
        mobileNotificationPopup.style.right = '10px';
        mobileNotificationPopup.style.left = 'auto';
        mobileNotificationPopup.style.bottom = 'auto';
        if (mobileNotificationPopup.classList.contains('hidden')) {
          mobileNotificationPopup.classList.remove('hidden');
          setTimeout(() => {
            mobileNotificationPopup.classList.add('show');
          }, 10);
        } else {
          mobileNotificationPopup.classList.remove('show');
          setTimeout(() => {
            mobileNotificationPopup.classList.add('hidden');
          }, 200);
        }
      });
      document.addEventListener('click', function (e) {
        if (!mobileNotificationPopup.classList.contains('hidden') &&
          !mobileNotificationBtn.contains(e.target) &&
          !mobileNotificationPopup.contains(e.target)) {
          mobileNotificationPopup.classList.remove('show');
          setTimeout(() => {
            mobileNotificationPopup.classList.add('hidden');
          }, 200);
        }
      });
    }
  });



  // Notification system functions
  let notificationSocket = null;
  let isAdmin = "<%= helpers.getUserRole && helpers.getUserRole(req) === 'admin' ? 'true' : 'false' %>";

  function initializeNotificationSystem() {
    if (typeof io !== 'undefined' && '<%= locals.session?.userId %>') {
      notificationSocket = io();

      if (isAdmin) {
        // Join admin notification room
        notificationSocket.emit('admin:join', {
          isAdmin: true,
          userId: '<%= locals.session?.userId %>'
        });
      } else {
        // Join user notification room
        notificationSocket.emit('user:join', {
          userId: '<%= locals.session?.userId %>'
        });
      }

      // Listen for real-time notifications
      notificationSocket.on('notification:new', (notification) => {
        addNotificationToDropdown(notification);
        updateNotificationBadge();
        showNotificationToast(notification);
      });

      notificationSocket.on('notification:updated', (data) => {
        updateNotificationInDropdown(data.id, { isRead: data.isRead });
        updateNotificationBadge();
      });

      notificationSocket.on('notification:allMarkedRead', () => {
        markAllNotificationsAsReadInDropdown();
        updateNotificationBadge();
      });

      notificationSocket.on('notification:deleted', (data) => {
        removeNotificationFromDropdown(data.id);
        updateNotificationBadge();
      });
    }

    // Mark all as read button
    const markAllReadBtn = document.getElementById('mark-all-read-btn');
    if (markAllReadBtn) {
      markAllReadBtn.addEventListener('click', markAllNotificationsAsRead);
    }
  }

  async function loadNotifications() {
    const loadingEl = document.getElementById('notification-loading');
    const emptyEl = document.getElementById('notification-empty');

    try {
      loadingEl.classList.remove('hidden');
      emptyEl.classList.add('hidden');

      const endpoint = isAdmin ? '/api/notifications/admin?limit=10' : '/api/notifications/user?limit=10';
      const response = await fetch(endpoint);
      const data = await response.json();

      if (data.success) {
        renderNotificationsInDropdown(data.notifications);
        updateNotificationBadge(data.unreadCount);
      } else {
        console.error('Failed to load notifications:', data.error);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      loadingEl.classList.add('hidden');
    }
  }

  async function loadDesktopNotifications() {
    const loadingEl = document.getElementById('desktop-notification-loading');
    const emptyEl = document.getElementById('desktop-notification-empty');

    try {
      loadingEl.classList.remove('hidden');
      emptyEl.classList.add('hidden');

      const endpoint = isAdmin ? '/api/notifications/admin?limit=10' : '/api/notifications/user?limit=10';
      const response = await fetch(endpoint);
      const data = await response.json();

      if (data.success) {
        renderDesktopNotificationsInDropdown(data.notifications);
        updateDesktopNotificationBadge(data.unreadCount);
      } else {
        console.error('Failed to load notifications:', data.error);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      loadingEl.classList.add('hidden');
    }
  }

  function renderNotificationsInDropdown(notifications) {
    const listEl = document.getElementById('notification-list');
    const emptyEl = document.getElementById('notification-empty');

    // Clear existing notifications (except loading and empty states)
    const existingNotifications = listEl.querySelectorAll('.notification-item');
    existingNotifications.forEach(item => item.remove());

    if (notifications.length === 0) {
      emptyEl.classList.remove('hidden');
      return;
    }

    emptyEl.classList.add('hidden');

    notifications.forEach(notification => {
      const notificationEl = createNotificationElement(notification);
      listEl.appendChild(notificationEl);
    });
  }

  function createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = `notification-item group px-4 py-3 border-b border-gray-700 hover:bg-dark-700 transition-colors cursor-pointer ${notification.is_read ? 'opacity-60' : ''}`;
    div.dataset.id = notification.id;

    const typeIcons = {
      info: 'ti-info-circle',
      warning: 'ti-alert-triangle',
      error: 'ti-alert-circle',
      success: 'ti-check-circle'
    };

    const typeColors = {
      info: 'text-primary bg-primary/20',
      warning: 'text-yellow-400 bg-yellow-500/20',
      error: 'text-red-400 bg-red-500/20',
      success: 'text-green-400 bg-green-500/20'
    };

    const priorityColors = {
      low: 'bg-gray-500',
      normal: 'bg-primary',
      high: 'bg-orange-500',
      critical: 'bg-red-500'
    };

    div.innerHTML = `
      <div class="flex items-start">
        <div class="shrink-0 mr-3">
          <div class="w-8 h-8 ${typeColors[notification.type]} rounded-full flex items-center justify-center">
            <i class="ti ${typeIcons[notification.type]} ${typeColors[notification.type].split(' ')[0]}"></i>
          </div>
        </div>
        <div class="flex-1">
          <div class="flex items-center space-x-2 mb-1">
            <p class="text-sm font-medium">${notification.title}</p>
            ${notification.priority !== 'normal' ? `<span class="px-1.5 py-0.5 text-xs rounded-full ${priorityColors[notification.priority]} text-white">${notification.priority.toUpperCase()}</span>` : ''}
            ${!notification.is_read ? '<span class="w-2 h-2 bg-primary rounded-full"></span>' : ''}
          </div>
          <p class="text-sm text-gray-300 mb-1">${notification.message}</p>
          <p class="text-xs text-gray-400">${new Date(notification.created_at).toLocaleString()}</p>
        </div>
        ${isAdmin ? `
        <div class="shrink-0 ml-2">
          <button class="delete-notification-btn text-gray-400 hover:text-red-400 p-1 opacity-0 group-hover:opacity-100 transition-opacity" data-id="${notification.id}" title="Delete notification">
            <i class="ti ti-trash text-xs"></i>
          </button>
        </div>
        ` : ''}
      </div>
    `;

    // Add click handler to mark as read
    div.addEventListener('click', (e) => {
      // Don't mark as read if clicking delete button
      if (e.target.closest('.delete-notification-btn')) {
        return;
      }
      if (!notification.is_read) {
        markNotificationAsRead(notification.id);
      }
    });

    // Add delete button handler (only for admins)
    if (isAdmin) {
      const deleteBtn = div.querySelector('.delete-notification-btn');
      if (deleteBtn) {
        deleteBtn.addEventListener('click', async (e) => {
          e.stopPropagation();
          const confirmed = await notifications.confirm(
            'Are you sure you want to delete this notification?',
            'Delete Notification',
            {
              confirmText: 'Delete',
              type: 'warning'
            }
          );
          if (confirmed) {
            deleteNotification(notification.id);
          }
        });
      }
    }

    return div;
  }



  function addNotificationToDropdown(notification) {
    const listEl = document.getElementById('notification-list');
    const emptyEl = document.getElementById('notification-empty');

    emptyEl.classList.add('hidden');

    const notificationEl = createNotificationElement(notification);
    listEl.insertBefore(notificationEl, listEl.firstChild);

    // Remove oldest notification if we have more than 10
    const notifications = listEl.querySelectorAll('.notification-item');
    if (notifications.length > 10) {
      notifications[notifications.length - 1].remove();
    }
  }

  function updateNotificationInDropdown(id, updates) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item && updates.isRead) {
      item.classList.add('opacity-60');
      const unreadDot = item.querySelector('.w-2.h-2.bg-blue-500');
      if (unreadDot) unreadDot.remove();
    }
  }

  function markAllNotificationsAsReadInDropdown() {
    const items = document.querySelectorAll('.notification-item');
    items.forEach(item => {
      item.classList.add('opacity-60');
      const unreadDot = item.querySelector('.w-2.h-2.bg-blue-500');
      if (unreadDot) unreadDot.remove();
    });
  }

  async function markNotificationAsRead(id) {
    try {
      const endpoint = isAdmin ? `/api/notifications/admin/${id}/mark-read` : `/api/notifications/user/${id}/mark-read`;
      const response = await fetch(endpoint, {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        updateNotificationInDropdown(id, { isRead: true });
        updateNotificationBadge();
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  async function markAllNotificationsAsRead() {
    try {
      const endpoint = isAdmin ? '/api/notifications/admin/mark-all-read' : '/api/notifications/user/mark-all-read';
      const response = await fetch(endpoint, {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        markAllNotificationsAsReadInDropdown();
        updateNotificationBadge(0);
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  async function deleteNotification(id) {
    // Only admins can delete notifications
    if (!isAdmin) {
      console.warn('Only admins can delete notifications');
      return;
    }

    try {
      const response = await fetch(`/api/notifications/admin/${id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        removeNotificationFromDropdown(id);
        updateNotificationBadge();
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  }

  function removeNotificationFromDropdown(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
      item.remove();

      // Show empty state if no notifications left
      const listEl = document.getElementById('notification-list');
      const emptyEl = document.getElementById('notification-empty');
      const remainingNotifications = listEl.querySelectorAll('.notification-item');

      if (remainingNotifications.length === 0) {
        emptyEl.classList.remove('hidden');
      }
    }
  }

  function renderDesktopNotificationsInDropdown(notifications) {
    const listEl = document.getElementById('desktop-notification-list');
    const emptyEl = document.getElementById('desktop-notification-empty');

    // Clear existing notifications (except loading and empty states)
    const existingNotifications = listEl.querySelectorAll('.notification-item');
    existingNotifications.forEach(item => item.remove());

    if (notifications.length === 0) {
      emptyEl.classList.remove('hidden');
      return;
    }

    emptyEl.classList.add('hidden');

    notifications.forEach(notification => {
      const notificationEl = createNotificationElement(notification);
      listEl.appendChild(notificationEl);
    });
  }

  function updateDesktopNotificationBadge(count = null) {
    const badge = document.getElementById('desktop-notification-badge');
    if (!badge) return;

    if (count !== null) {
      if (count > 0) {
        badge.classList.remove('hidden');
      } else {
        badge.classList.add('hidden');
      }
    } else {
      // Count unread notifications in dropdown
      const listEl = document.getElementById('desktop-notification-list');
      const unreadNotifications = listEl.querySelectorAll('.notification-item:not(.opacity-60)');
      if (unreadNotifications.length > 0) {
        badge.classList.remove('hidden');
      } else {
        badge.classList.add('hidden');
      }
    }
  }

  function updateNotificationBadge(count = null) {
    const badge = document.getElementById('notification-badge');
    const desktopBadge = document.getElementById('desktop-notification-badge');

    if (count !== null) {
      if (count > 0) {
        if (badge) badge.classList.remove('hidden');
        if (desktopBadge) desktopBadge.classList.remove('hidden');
      } else {
        if (badge) badge.classList.add('hidden');
        if (desktopBadge) desktopBadge.classList.add('hidden');
      }
    } else {
      // Count unread notifications in dropdown
      const unreadItems = document.querySelectorAll('.notification-item:not(.opacity-60)');
      if (unreadItems.length > 0) {
        badge.classList.remove('hidden');
      } else {
        badge.classList.add('hidden');
      }
    }
  }

  function showNotificationToast(notification) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 max-w-sm bg-dark-800 border border-gray-700 rounded-lg shadow-xl p-4 transform transition-all duration-300 translate-x-full`;

    const typeColors = {
      info: 'border-primary',
      warning: 'border-yellow-500',
      error: 'border-red-500',
      success: 'border-green-500'
    };

    toast.classList.add(typeColors[notification.type] || 'border-primary');

    toast.innerHTML = `
      <div class="flex items-start">
        <div class="flex-1">
          <h4 class="font-medium text-white text-sm">${notification.title}</h4>
          <p class="text-gray-300 text-sm mt-1">${notification.message}</p>
        </div>
        <button class="ml-2 text-gray-400 hover:text-white" onclick="this.parentElement.parentElement.remove()">
          <i class="ti ti-x"></i>
        </button>
      </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
      toast.classList.add('translate-x-full');
      setTimeout(() => {
        if (toast.parentElement) {
          toast.remove();
        }
      }, 300);
    }, 5000);
  }
</script>

<!-- Modern Notification System -->
<script src="/js/notifications.js"></script>
</body>
</html>