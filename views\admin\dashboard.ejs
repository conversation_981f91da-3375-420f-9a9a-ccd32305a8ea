<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white"><%= t('admin.title') %></h1>
      <p class="text-gray-400 mt-1"><%= t('admin.subtitle') %></p>
    </div>
    <div class="flex items-center space-x-4">
      <span class="text-sm text-gray-400"><%= t('admin.welcome', { username: user.username }) %></span>
      <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
        <i class="ti ti-user text-white text-sm"></i>
      </div>
    </div>
  </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- Total Users -->
          <div class="card-enhanced p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.total_users') %></p>
                <p class="text-2xl font-bold gradient-text" data-stat="total_users"><%= stats.total_users || 0 %></p>
                <p class="text-green-400 text-sm"><%= stats.active_users || 0 %> <%= t('admin.active') %></p>
              </div>
              <div class="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                <i class="ti ti-users text-white text-xl"></i>
              </div>
            </div>
          </div>

          <!-- Live Streams -->
          <div class="card-enhanced p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.live_streams') %></p>
                <p class="text-2xl font-bold text-white" data-stat="live_streams"><%= stats.live_streams || 0 %></p>
                <p class="text-gray-400 text-sm"><%= stats.total_streams || 0 %> <%= t('admin.total') %></p>
              </div>
              <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                <i class="ti ti-broadcast text-white text-xl"></i>
              </div>
            </div>
          </div>

          <!-- Total Videos -->
          <div class="card-enhanced p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.total_videos') %></p>
                <p class="text-2xl font-bold text-white" data-stat="total_videos"><%= stats.total_videos || 0 %></p>
                <p class="text-gray-400 text-sm"><%= stats.total_storage_gb || 0 %>GB <%= t('admin.used') %></p>
              </div>
              <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <i class="ti ti-video text-white text-xl"></i>
              </div>
            </div>
          </div>

          <!-- Active Subscriptions -->
          <div class="card-enhanced p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.subscriptions') %></p>
                <p class="text-2xl font-bold text-white" data-stat="active_subscriptions"><%= stats.active_subscriptions || 0 %></p>
                <p class="text-green-400 text-sm"><%= t('admin.active') %></p>
              </div>
              <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                <i class="ti ti-credit-card text-white text-xl"></i>
              </div>
            </div>
          </div>
      </div>

      <!-- System Resources -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <!-- CPU Usage -->
          <div class="card-enhanced p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.cpu_usage') %></p>
                <p class="text-2xl font-bold text-white"><span id="cpu-usage"><%= typeof systemStats !== 'undefined' ? systemStats.cpu.usage : 0 %></span>%</p>
                <p class="text-gray-400 text-sm"><span id="cpu-cores"><%= typeof systemStats !== 'undefined' ? systemStats.cpu.cores : 0 %></span> <%= t('admin.cores') %></p>
              </div>
              <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                <i class="ti ti-cpu text-white text-xl"></i>
              </div>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2.5 mt-4">
              <div id="cpu-bar" class="bg-gradient-to-r from-orange-500 to-orange-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= typeof systemStats !== 'undefined' ? systemStats.cpu.usage : 0 %>%"></div>
            </div>
          </div>

          <!-- Memory Usage -->
          <div class="card-enhanced p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.memory_usage') %></p>
                <p class="text-2xl font-bold text-white">
                  <span id="memory-used"><%= typeof systemStats !== 'undefined' ? systemStats.memory.used : '0 GB' %></span><span class="text-sm text-gray-400"> / <span id="memory-total"><%= typeof systemStats !== 'undefined' ? systemStats.memory.total : '0 GB' %></span></span>
                </p>
                <p class="text-gray-400 text-sm"><span id="memory-percentage"><%= typeof systemStats !== 'undefined' ? systemStats.memory.usagePercent : 0 %></span>% <%= t('admin.used') %></p>
              </div>
              <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center">
                <i class="ti ti-device-laptop text-white text-xl"></i>
              </div>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2.5 mt-4">
              <div id="memory-bar" class="bg-gradient-to-r from-cyan-500 to-cyan-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= typeof systemStats !== 'undefined' ? systemStats.memory.usagePercent : 0 %>%"></div>
            </div>
          </div>

          <!-- Storage Usage -->
          <div class="card-enhanced p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.disk_usage') %></p>
                <p class="text-2xl font-bold text-white">
                  <span id="storage-used"><%= typeof systemStats !== 'undefined' && systemStats.storage ? systemStats.storage.used : '0 GB' %></span><span class="text-sm text-gray-400"> / <span id="storage-total"><%= typeof systemStats !== 'undefined' && systemStats.storage ? systemStats.storage.total : '0 GB' %></span></span>
                </p>
                <p class="text-gray-400 text-sm"><span id="storage-percentage"><%= typeof systemStats !== 'undefined' && systemStats.storage ? systemStats.storage.usagePercent : 0 %></span>% <%= t('admin.used') %></p>
              </div>
              <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <i class="ti ti-device-floppy text-white text-xl"></i>
              </div>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2.5 mt-4">
              <div id="storage-bar" class="bg-gradient-to-r from-purple-500 to-purple-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= typeof systemStats !== 'undefined' && systemStats.storage ? systemStats.storage.usagePercent : 0 %>%"></div>
            </div>
          </div>
      </div>

      <!-- Stream Status Management -->
      <div class="card-enhanced p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-lg font-semibold text-white"><%= t('admin.stream_status_sync') %></h3>
            <p class="text-gray-400 text-sm"><%= t('admin.stream_status_sync_desc') %></p>
          </div>
          <div class="flex items-center space-x-3">
            <span id="sync-status" class="text-sm text-gray-400">Ready</span>
            <button id="sync-streams-btn" onclick="syncStreamStatuses()" class="btn-primary">
              <i class="ti ti-refresh mr-2"></i>
              <%= t('admin.sync_now') %>
            </button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-dark-700 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.database_live') %></p>
                <p class="text-xl font-bold text-white" id="db-live-count">-</p>
              </div>
              <i class="ti ti-database text-blue-400 text-xl"></i>
            </div>
          </div>

          <div class="bg-dark-700 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.memory_active') %></p>
                <p class="text-xl font-bold text-white" id="memory-active-count">-</p>
              </div>
              <i class="ti ti-cpu text-green-400 text-xl"></i>
            </div>
          </div>

          <div class="bg-dark-700 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm"><%= t('admin.inconsistencies') %></p>
                <p class="text-xl font-bold text-white" id="inconsistency-count">-</p>
              </div>
              <i class="ti ti-alert-triangle text-orange-400 text-xl"></i>
            </div>
          </div>
        </div>

        <div id="sync-log" class="mt-4 bg-dark-700 rounded-lg p-4 hidden">
          <h4 class="text-white font-medium mb-2">Sync Log</h4>
          <div id="sync-log-content" class="text-sm text-gray-300 font-mono max-h-32 overflow-y-auto"></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <!-- Recent Users -->
          <div class="card-enhanced">
            <div class="p-6 border-b border-gray-700">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white"><%= t('admin.recent_users') %></h3>
                <a href="/admin/users" class="text-primary hover:text-primary-light text-sm"><%= t('admin.view_all') %></a>
              </div>
            </div>
            <div class="p-6">
              <% users.slice(0, 5).forEach(function(user) { %>
                <div class="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <i class="ti ti-user text-white text-sm"></i>
                    </div>
                    <div>
                      <p class="text-white font-medium"><%= user.username %></p>
                      <p class="text-gray-400 text-sm"><%= user.email || t('admin.no_email') %></p>
                    </div>
                  </div>
                  <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.role === 'admin' ? 'bg-red-100 text-red-800' : user.role === 'moderator' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800' %>">
                      <%= user.role %>
                    </span>
                    <p class="text-gray-400 text-xs mt-1"><%= user.plan_type %></p>
                  </div>
                </div>
              <% }); %>
            </div>
          </div>

          <!-- Subscription Plans -->
          <div class="card-enhanced">
            <div class="p-6 border-b border-gray-700">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white"><%= t('admin.subscription_plans') %></h3>
                <a href="/admin/plans" class="text-primary hover:text-primary-light text-sm"><%= t('admin.manage') %></a>
              </div>
            </div>
            <div class="p-6">
              <% plans.forEach(function(plan) { %>
                <div class="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
                  <div>
                    <p class="text-white font-medium"><%= plan.name %></p>
                    <p class="text-gray-400 text-sm">
                      <%= plan.max_streaming_slots === -1 ? t('admin.unlimited') : plan.max_streaming_slots %> <%= t('admin.slots') %>,
                      <% if (plan.max_storage_gb < 1) { %>
                        <%= Math.round(plan.max_storage_gb * 1024) %>MB
                      <% } else { %>
                        <%= plan.max_storage_gb %>GB
                      <% } %> •
                      <span class="text-primary"><%= plan.subscriber_count || 0 %> <%= t('admin.subscribers') %></span>
                    </p>
                  </div>
                  <div class="text-right">
                    <p class="text-white font-medium">
                      <%
                        let currencySymbol = '$';
                        if (plan.currency === 'IDR') currencySymbol = 'Rp';
                        else if (plan.currency === 'EUR') currencySymbol = '€';
                        else if (plan.currency === 'GBP') currencySymbol = '£';
                        else if (plan.currency === 'USD') currencySymbol = '$';
                      %>
                      <%= currencySymbol %><%= plan.price %>
                    </p>
                    <p class="text-gray-400 text-xs"><%= plan.billing_period %></p>
                  </div>
                </div>
              <% }); %>
            </div>
          </div>
      </div>

      <!-- Quick Actions Buttons -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <a href="/admin/users" class="card-enhanced p-6 text-center transition-all hover:scale-105">
            <i class="ti ti-user-cog text-primary text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2"><%= t('admin.manage_users') %></h4>
            <p class="text-gray-400 text-sm"><%= t('admin.manage_users_desc') %></p>
          </a>

          <a href="/admin/subscriptions" class="card-enhanced p-6 text-center transition-all hover:scale-105">
            <i class="ti ti-crown text-green-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2"><%= t('admin.subscription_management') %></h4>
            <p class="text-gray-400 text-sm"><%= t('admin.subscription_management_desc') %></p>
          </a>

          <a href="/admin/plans" class="card-enhanced p-6 text-center transition-all hover:scale-105">
            <i class="ti ti-settings text-blue-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2"><%= t('admin.subscription_plans') %></h4>
            <p class="text-gray-400 text-sm"><%= t('admin.subscription_plans_desc') %></p>
          </a>

          <a href="/admin/stats" class="card-enhanced p-6 text-center transition-all hover:scale-105">
            <i class="ti ti-chart-bar text-purple-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2"><%= t('admin.analytics') %></h4>
            <p class="text-gray-400 text-sm"><%= t('admin.analytics_desc') %></p>
          </a>

          <a href="/admin/load-balancer" class="card-enhanced p-6 text-center transition-all hover:scale-105">
            <i class="ti ti-adjustments text-cyan-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2"><%= t('admin.load_balancer') %></h4>
            <p class="text-gray-400 text-sm"><%= t('admin.load_balancer_desc') %></p>
          </a>

          <a href="/admin/performance" class="card-enhanced p-6 text-center transition-all hover:scale-105">
            <i class="ti ti-dashboard text-emerald-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2"><%= t('admin.performance') %></h4>
            <p class="text-gray-400 text-sm"><%= t('admin.performance_desc') %></p>
          </a>

          <a href="/admin/stream-monitor" class="card-enhanced p-6 text-center transition-all hover:scale-105">
            <i class="ti ti-video text-orange-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2"><%= t('admin.stream_monitor') %></h4>
            <p class="text-gray-400 text-sm"><%= t('admin.stream_monitor_desc') %></p>
          </a>
      </div>

    <script>
      // Stream status sync functionality
      async function syncStreamStatuses() {
        const btn = document.getElementById('sync-streams-btn');
        const status = document.getElementById('sync-status');
        const logDiv = document.getElementById('sync-log');
        const logContent = document.getElementById('sync-log-content');

        try {
          // Update UI
          btn.disabled = true;
          btn.innerHTML = '<i class="ti ti-loader animate-spin mr-2"></i>Syncing...';
          status.textContent = 'Syncing...';
          status.className = 'text-sm text-yellow-400';

          // Show log
          logDiv.classList.remove('hidden');
          logContent.innerHTML = 'Starting stream status synchronization...<br>';

          // Call sync API
          const response = await fetch('/api/admin/sync-stream-status', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const result = await response.json();

          if (result.success) {
            status.textContent = 'Sync completed successfully';
            status.className = 'text-sm text-green-400';
            logContent.innerHTML += 'Synchronization completed successfully!<br>';

            // Refresh stream status data
            await updateStreamStatusData();
          } else {
            throw new Error(result.error || 'Sync failed');
          }
        } catch (error) {
          status.textContent = 'Sync failed';
          status.className = 'text-sm text-red-400';
          logContent.innerHTML += `Error: ${error.message}<br>`;
        } finally {
          // Reset button
          btn.disabled = false;
          btn.innerHTML = '<i class="ti ti-refresh mr-2"></i><%= t("admin.sync_now") %>';

          // Auto-hide log after 5 seconds
          setTimeout(() => {
            status.textContent = 'Ready';
            status.className = 'text-sm text-gray-400';
          }, 5000);
        }
      }

      // Update stream status data
      async function updateStreamStatusData() {
        try {
          const response = await fetch('/api/streams/status');
          const data = await response.json();

          if (data.success) {
            const dbLive = data.streams.filter(s => s.status === 'live').length;
            const memoryActive = data.activeCount || 0;
            const inconsistencies = data.streams.filter(s => s.actualStatus === 'inconsistent').length;

            document.getElementById('db-live-count').textContent = dbLive;
            document.getElementById('memory-active-count').textContent = memoryActive;
            document.getElementById('inconsistency-count').textContent = inconsistencies;

            // Update inconsistency count color
            const inconsistencyElement = document.getElementById('inconsistency-count');
            if (inconsistencies > 0) {
              inconsistencyElement.className = 'text-xl font-bold text-orange-400';
            } else {
              inconsistencyElement.className = 'text-xl font-bold text-white';
            }
          }
        } catch (error) {
          // console.error('Failed to fetch stream status data:', error); // Cleaned for production
        }
      }

      // Update system stats
      async function updateSystemStats() {
        try {
          const response = await fetch('/api/system-stats');
          const data = await response.json();

          // Update CPU stats
          document.getElementById('cpu-usage').textContent = data.cpu.usage || 0;
          document.getElementById('cpu-cores').textContent = data.cpu.cores || 0;
          document.getElementById('cpu-bar').style.width = (data.cpu.usage || 0) + '%';

          // Update Memory stats
          document.getElementById('memory-used').textContent = data.memory.used || '0 GB';
          document.getElementById('memory-total').textContent = data.memory.total || '0 GB';
          document.getElementById('memory-percentage').textContent = data.memory.usagePercent || 0;
          document.getElementById('memory-bar').style.width = (data.memory.usagePercent || 0) + '%';

          // Update Storage stats
          if (data.storage) {
            document.getElementById('storage-used').textContent = data.storage.used || '0 GB';
            document.getElementById('storage-total').textContent = data.storage.total || '0 GB';
            document.getElementById('storage-percentage').textContent = data.storage.usagePercent || 0;
            document.getElementById('storage-bar').style.width = (data.storage.usagePercent || 0) + '%';
          }
        } catch (error) {
          // console.error('Failed to fetch system stats:', error); // Cleaned for production
        }
      }

      // Auto-refresh stats every 60 seconds (reduced frequency)
      setInterval(async () => {
        try {
          const response = await fetch('/admin/stats');
          const stats = await response.json();

          // Update stats display
          document.querySelector('[data-stat="total_users"]').textContent = stats.total_users || 0;
          document.querySelector('[data-stat="live_streams"]').textContent = stats.live_streams || 0;
          document.querySelector('[data-stat="total_videos"]').textContent = stats.total_videos || 0;
          document.querySelector('[data-stat="active_subscriptions"]').textContent = stats.active_subscriptions || 0;
        } catch (error) {
          // console.error('Failed to refresh stats:', error); // Cleaned for production
        }
      }, 60000);

      // Update system stats every 30 seconds (reduced frequency)
      updateSystemStats();
      setInterval(updateSystemStats, 30000);

      // Initialize and auto-refresh stream status data
      updateStreamStatusData();
      setInterval(updateStreamStatusData, 30000);
    </script>
