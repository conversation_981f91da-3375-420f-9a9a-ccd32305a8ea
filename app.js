require('dotenv').config();
require('./services/logger.js');
const express = require('express');
const path = require('path');
const engine = require('ejs-mate');
const os = require('os');
const multer = require('multer');
const fs = require('fs');
const csrf = require('csrf');
const { v4: uuidv4 } = require('uuid');
const session = require('express-session');
const bcrypt = require('bcrypt');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const User = require('./models/User');
const Subscription = require('./models/Subscription');
const Permission = require('./models/Permission');
const { db, checkIfUsersExist } = require('./db/database');
const systemMonitor = require('./services/systemMonitor');
const { uploadVideo, uploadChunk } = require('./middleware/uploadMiddleware');
const chunkedUploadService = require('./utils/chunkedUploadService');
const QuotaMiddleware = require('./middleware/quotaMiddleware');
const seoMiddleware = require('./middleware/seoMiddleware');
const { ensureDirectories } = require('./utils/storage');
const { getVideoInfo, generateThumbnail } = require('./utils/videoProcessor');
const videoProcessingService = require('./services/videoProcessingService');

// Performance optimizations
const dbOptimizer = require('./db/optimizations');
const cacheService = require('./services/cacheService');
const staticOptimization = require('./middleware/staticOptimization');
const performanceMonitor = require('./services/performanceMonitor');
const Video = require('./models/Video');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
const streamingService = require('./services/streamingService');
const schedulerService = require('./services/schedulerService');

// Import routes
const adminRoutes = require('./routes/admin');
const subscriptionRoutes = require('./routes/subscription');
const notificationRoutes = require('./routes/notifications');

// Import notification service
const notificationService = require('./services/notificationService');

// Import subscription monitor
const subscriptionMonitor = require('./services/subscriptionMonitor');

// Import i18n middleware
const { i18n, setLocale, addHelpers } = require('./middleware/i18n');
ffmpeg.setFfmpegPath(ffmpegInstaller.path);
process.on('unhandledRejection', (reason, promise) => {
  console.error('-----------------------------------');
  console.error('UNHANDLED REJECTION AT:', promise);
  console.error('REASON:', reason);
  console.error('-----------------------------------');
});
process.on('uncaughtException', (error) => {
  console.error('-----------------------------------');
  console.error('UNCAUGHT EXCEPTION:', error);
  console.error('-----------------------------------');
});
const app = express();
const http = require('http');
const { Server } = require('socket.io');
const port = process.env.PORT || 7575;
const tokens = new csrf();

// Create HTTP server and Socket.IO instance
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Database migration function
async function migrateDatabase() {
  return new Promise((resolve, reject) => {
    // console.log('Checking database schema...'); // Removed for production
    // Add missing columns to videos table
    db.run(`ALTER TABLE videos ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding created_at to videos:', err.message);
      }
    });

    db.run(`ALTER TABLE videos ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding updated_at to videos:', err.message);
      }
    });

    // Add missing columns to users table
    db.run(`ALTER TABLE users ADD COLUMN email TEXT UNIQUE`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding email to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN role TEXT DEFAULT "user"`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding role to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN plan_type TEXT DEFAULT "free"`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding plan_type to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN max_streaming_slots INTEGER DEFAULT 1`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding max_streaming_slots to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN max_storage_gb INTEGER DEFAULT 5`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding max_storage_gb to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN used_storage_gb REAL DEFAULT 0`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding used_storage_gb to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT 1`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding is_active to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding updated_at to users:', err.message);
      }
    });

    // Add missing columns to streams table
    db.run(`ALTER TABLE streams ADD COLUMN user_id TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding user_id to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding updated_at to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN orientation TEXT DEFAULT 'horizontal'`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding orientation to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN loop_video BOOLEAN DEFAULT 1`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding loop_video to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN status_updated_at TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding status_updated_at to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN start_time TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding start_time to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN end_time TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding end_time to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN duration INTEGER`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding duration to streams:', err.message);
      }
    });

    // Add video processing columns
    db.run(`ALTER TABLE videos ADD COLUMN processing_status TEXT DEFAULT 'pending'`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding processing_status to videos:', err.message);
      }
    });

    db.run(`ALTER TABLE videos ADD COLUMN streaming_ready_path TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding streaming_ready_path to videos:', err.message);
      }
    });

    db.run(`ALTER TABLE videos ADD COLUMN original_filepath TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding original_filepath to videos:', err.message);
      }
    });

    // Add missing columns to stream_history table
    db.run(`ALTER TABLE stream_history ADD COLUMN user_id TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding user_id to stream_history:', err.message);
      }

      // Update existing records
      setTimeout(() => {
        // Update videos without created_at
        db.run(`UPDATE videos SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL`);
        db.run(`UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL`);

        // console.log('✅ Database migration completed'); // Removed for production
        resolve();
      }, 1000);
    });
  });
}

// Function to create initial notifications if none exist
async function createInitialNotifications() {
  try {
    const Notification = require('./models/Notification');

    // Check if notifications already exist
    const existingCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM notifications', [], (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });

    if (existingCount === 0) {
      const isProduction = process.env.NODE_ENV === 'production';
      if (!isProduction) {
        // console.info('Creating initial notifications...'); // Removed for production
      }

      // Create welcome notification
      await notificationService.createNotification({
        title: 'Welcome to StreamOnPod!',
        message: 'StreamOnPod system has been initialized successfully. You can now manage your streaming platform.',
        type: 'success',
        category: 'system',
        priority: 'normal'
      });

      // Create system status notification
      await notificationService.createNotification({
        title: 'System Status',
        message: 'All systems are operational and ready for streaming.',
        type: 'info',
        category: 'system',
        priority: 'low'
      });

      if (!isProduction) {
        // console.info('✅ Initial notifications created'); // Removed for production
      }
    }
  } catch (error) {
    console.error('Error creating initial notifications:', error);
  }
}

// Run migration before starting the app
migrateDatabase().then(() => {
  const isProduction = process.env.NODE_ENV === 'production';
  if (!isProduction) {
    // console.info('Database migration completed successfully'); // Removed for production
  }
  // Create initial notifications after migration
  return createInitialNotifications();
}).catch((error) => {
  console.error('Database migration failed:', error);
});

ensureDirectories();
ensureDirectories();
app.locals.helpers = {
  getUsername: function (req) {
    if (req.session && req.session.username) {
      return req.session.username;
    }
    return 'User';
  },
  getAvatar: function (req) {
    if (req.session && req.session.userId) {
      const avatarPath = req.session.avatar_path;
      if (avatarPath) {
        return `<img src="${avatarPath}" alt="${req.session.username || 'User'}'s Profile" class="w-full h-full object-cover" onerror="this.onerror=null; this.src='/images/default-avatar.png';">`;
      }
    }
    return '<img src="/images/default-avatar.png" alt="Default Profile" class="w-full h-full object-cover">';
  },
  getPlatformIcon: function (platform) {
    switch (platform) {
      case 'YouTube': return 'brand-youtube';
      case 'Facebook': return 'brand-facebook';
      case 'Twitch': return 'brand-twitch';
      case 'TikTok': return 'brand-tiktok';
      case 'Instagram': return 'brand-instagram';
      case 'Shopee Live': return 'shopping-bag';
      case 'Restream.io': return 'live-photo';
      default: return 'broadcast';
    }
  },
  getPlatformColor: function (platform) {
    switch (platform) {
      case 'YouTube': return 'red-500';
      case 'Facebook': return 'blue-500';
      case 'Twitch': return 'purple-500';
      case 'TikTok': return 'gray-100';
      case 'Instagram': return 'pink-500';
      case 'Shopee Live': return 'orange-500';
      case 'Restream.io': return 'teal-500';
      default: return 'gray-400';
    }
  },
  formatDateTime: function (isoString) {
    if (!isoString) return '--';
    const date = new Date(isoString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  },
  formatDuration: function (seconds) {
    if (!seconds) return '--';
    const hours = Math.floor(seconds / 3600).toString().padStart(2, '0');
    const minutes = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${hours}:${minutes}:${secs}`;
  },
  getUserRole: function (req) {
    return req.session.userRole || 'user';
  }
};
// Trust proxy FIRST (important for tunneling)
if (process.env.TRUST_PROXY === 'true') {
  app.set('trust proxy', 1);
  // console.log('✅ Proxy trust enabled for tunneling'); // Removed for production
}

// Session configuration optimized for tunnel compatibility
const isProduction = process.env.NODE_ENV === 'production';
// Disable console logging for production
const enableConsoleLogging = !isProduction; // Only enable in development
// Detect tunnel mode from request headers instead of BASE_URL
const isTunnelMode = false; // Always false to ensure compatibility

// Simple in-memory session store for debugging
const MemoryStore = require('memorystore')(session);

const sessionConfig = {
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  store: new MemoryStore({
    checkPeriod: 86400000 // prune expired entries every 24h
  }),
  name: 'streamonpod.sid',
  cookie: {
    httpOnly: true,
    secure: false, // Always false for compatibility with both local and tunnel
    maxAge: 24 * 60 * 60 * 1000,
    sameSite: 'lax' // Always lax for better compatibility
  }
};

// Log session configuration for debugging
if (!isProduction && enableConsoleLogging) {
  // console.log('🔧 Session Configuration:'); // Removed for production
  // console.log(`   Tunnel mode: ${isTunnelMode}`); // Removed for production
  // console.log(`   Secure cookies: ${sessionConfig.cookie.secure}`); // Removed for production
  // console.log(`   SameSite: ${sessionConfig.cookie.sameSite}`); // Removed for production
  // console.log(`   Domain: ${sessionConfig.cookie.domain || 'default'}`); // Removed for production
  // console.log(`   Trust proxy: ${process.env.TRUST_PROXY === 'true'}`); // Removed for production
}

app.use(session(sessionConfig));

// Add i18n middleware
app.use(i18n.init);
app.use(setLocale);
app.use(addHelpers);
app.use(async (req, res, next) => {
  if (req.session && req.session.userId) {
    try {
      if (!isProduction && enableConsoleLogging) {
        // console.log('🔍 Loading user for session:', req.session.userId); // Removed for production
      }
      const user = await User.findById(req.session.userId);
      if (!isProduction && enableConsoleLogging) {
        // console.log('👤 User loaded:', user ? user.username : 'null'); // Removed for production
      }
      if (user) {
        req.session.username = user.username;
        req.session.avatar_path = user.avatar_path;
        req.session.userRole = user.role || 'user';
        if (user.email) req.session.email = user.email;
        res.locals.user = {
          id: user.id,
          username: user.username,
          avatar_path: user.avatar_path,
          email: user.email,
          role: user.role
        };
      }
    } catch (error) {
      console.error('❌ Error loading user:', error);
      // Don't fail the request, just continue without user data
      // This prevents the cache service errors from breaking the app
    }
  }
  res.locals.req = req;
  next();
});
app.use(function (req, res, next) {
  if (!req.session.csrfSecret) {
    req.session.csrfSecret = uuidv4();
  }
  res.locals.csrfToken = tokens.create(req.session.csrfSecret);
  next();
});
app.engine('ejs', engine);
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(express.static(path.join(__dirname, 'public')));
app.use('/uploads', function (req, res, next) {
  res.header('Cache-Control', 'no-cache');
  res.header('Pragma', 'no-cache');
  res.header('Expires', '0');
  next();
});

// Serve test file for debugging
app.get('/test_chunked_upload.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'test_chunked_upload.html'));
});

// Configure body parser with proper limits and timeout
const maxFileSize = parseInt(process.env.MAX_FILE_SIZE) || 2147483648; // 2GB default
console.log(`🔧 Server configuration: MAX_FILE_SIZE = ${maxFileSize} bytes (${Math.round(maxFileSize / (1024 * 1024))}MB)`);
app.use(express.urlencoded({
  extended: true,
  limit: maxFileSize,
  parameterLimit: 50000
}));
app.use(express.json({
  limit: maxFileSize
}));

// Increase server timeout for large uploads
app.use((req, res, next) => {
  // Set timeout to 10 minutes for upload endpoints
  if (req.path.includes('/upload') || req.path.includes('/api/videos')) {
    req.setTimeout(600000); // 10 minutes
    res.setTimeout(600000); // 10 minutes
  }
  next();
});

// Performance monitoring middleware
app.use(performanceMonitor.requestTracker());

// Static file optimization middleware
app.use(staticOptimization.middleware());

// SEO middleware for structured data and meta tags
app.use(seoMiddleware);

// Add no-cache headers for all HTML pages to prevent cache issues
app.use((req, res, next) => {
  // Log incoming requests only in development
  if (!isProduction && enableConsoleLogging) {
    console.log(`📥 ${req.method} ${req.path} - Session: ${req.session?.userId || 'none'} - User-Agent: ${req.get('User-Agent')?.substring(0, 50)}...`);
  }

  // Only apply to HTML pages, not API endpoints or static files
  if (!req.path.startsWith('/api/') && !req.path.startsWith('/public/') && !req.path.startsWith('/uploads/')) {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  next();
});
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const dir = './public/uploads/avatars';
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    cb(null, dir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = file.originalname.split('.').pop();
    cb(null, 'avatar-' + uniqueSuffix + '.' + ext);
  }
});
const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 },
  fileFilter: function (req, file, cb) {
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});
const videoStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, 'public', 'uploads', 'videos'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    let fileName = `video-${uniqueSuffix}${ext}`;
    let fullPath = path.join(__dirname, 'public', 'uploads', 'videos', fileName);
    let counter = 1;
    while (fs.existsSync(fullPath)) {
      fileName = `video-${uniqueSuffix}-${counter}${ext}`;
      fullPath = path.join(__dirname, 'public', 'uploads', 'videos', fileName);
      counter++;
    }
    cb(null, fileName);
  }
});
const videoUpload = multer({
  storage: videoStorage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 2147483648, // 2GB default
    files: 1
  },
  fileFilter: function (req, file, cb) {
    // Only support formats that work well with ffmpeg copy method for better performance
    if (!file.mimetype.match(/^video\/(mp4|quicktime)$/)) {
      return cb(new Error('Only MP4 and MOV video files are allowed for optimal performance!'), false);
    }
    cb(null, true);
  }
});
const csrfProtection = function (req, res, next) {
  // Allow disabling CSRF for debugging (only in development)
  if (process.env.DISABLE_CSRF === 'true' && !isProduction) {
    if (!isProduction && enableConsoleLogging) {
      // console.log('⚠️  CSRF protection disabled for debugging'); // Removed for production
    }
    return next();
  }

  // Skip CSRF for login and setup-account endpoints
  if ((req.path === '/login' && req.method === 'POST') ||
    (req.path === '/setup-account' && req.method === 'POST')) {
    return next();
  }

  // Skip CSRF for API endpoints that use authentication middleware
  if (req.path.startsWith('/api/') && req.method === 'POST') {
    return next();
  }

  const token = req.body._csrf || req.query._csrf || req.headers['x-csrf-token'];
  if (!token || !tokens.verify(req.session.csrfSecret, token)) {
    if (!isProduction && enableConsoleLogging) {
      console.error('CSRF validation failed:', {
        path: req.path,
        method: req.method,
        hasToken: !!token,
        hasSecret: !!req.session.csrfSecret
      });
    }

    // For API requests, return JSON error
    if (req.xhr || req.headers.accept?.indexOf('json') > -1 || req.path.startsWith('/api/')) {
      return res.status(403).json({
        success: false,
        error: 'CSRF validation failed. Please refresh the page and try again.'
      });
    }

    return res.status(403).render('error', {
      title: 'Error',
      error: 'CSRF validation failed. Please try again.'
    });
  }
  next();
};
const isAuthenticated = async (req, res, next) => {
  if (req.session.userId) {
    // Check for expired trial
    try {
      const activeTrial = await User.hasActiveTrial(req.session.userId);

      // If user had trial but it's expired, remove it
      if (!activeTrial) {
        const user = await User.findById(req.session.userId);
        if (user && user.trial_end_date && new Date(user.trial_end_date) < new Date()) {
          await User.removeExpiredTrial(req.session.userId);
        }
      }
    } catch (error) {
      console.error('Error checking trial status:', error);
      // Continue with request even if trial check fails
    }

    return next();
  }
  res.redirect('/login');
};

// Enhanced authentication middleware with quota checking
const isAuthenticatedWithQuota = [
  isAuthenticated,
  QuotaMiddleware.checkActiveAccount()
];
app.use('/uploads', function (req, res, next) {
  res.header('Cache-Control', 'no-cache');
  res.header('Pragma', 'no-cache');
  res.header('Expires', '0');
  next();
});
app.use('/uploads/avatars', (req, res, next) => {
  const file = path.join(__dirname, 'public', 'uploads', 'avatars', path.basename(req.path));
  if (fs.existsSync(file)) {
    const ext = path.extname(file).toLowerCase();
    let contentType = 'application/octet-stream';
    if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';
    else if (ext === '.png') contentType = 'image/png';
    else if (ext === '.gif') contentType = 'image/gif';
    res.header('Content-Type', contentType);
    res.header('Cache-Control', 'max-age=60, must-revalidate');
    fs.createReadStream(file).pipe(res);
  } else {
    next();
  }
});

// Serve thumbnails with proper headers and fallback
app.use('/uploads/thumbnails', (req, res, next) => {
  const file = path.join(__dirname, 'public', 'uploads', 'thumbnails', path.basename(req.path));
  if (fs.existsSync(file)) {
    const ext = path.extname(file).toLowerCase();
    let contentType = 'application/octet-stream';
    if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';
    else if (ext === '.png') contentType = 'image/png';
    else if (ext === '.gif') contentType = 'image/gif';
    else if (ext === '.webp') contentType = 'image/webp';

    res.header('Content-Type', contentType);
    res.header('Cache-Control', 'public, max-age=3600'); // Cache thumbnails for 1 hour
    res.header('Access-Control-Allow-Origin', '*'); // Allow cross-origin for social media
    fs.createReadStream(file).pipe(res);
  } else {
    // Return 404 for missing thumbnails instead of falling through
    res.status(404).json({ error: 'Thumbnail not found' });
  }
});
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5,
  standardHeaders: true,
  legacyHeaders: false,
  // Fix for undefined IP address error
  keyGenerator: (req) => {
    // Safely get IP address with fallback
    const ip = req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress || 'unknown';
    return ip;
  },
  // Skip rate limiting if IP cannot be determined
  skip: (req) => {
    const ip = req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress;
    return !ip || ip === 'unknown';
  },
  handler: (req, res) => {
    res.status(429).render('login', {
      title: 'Login',
      error: 'Too many login attempts. Please try again in 15 minutes.'
    });
  },
  requestWasSuccessful: (request, response) => {
    return response.statusCode < 400;
  }
});
const loginDelayMiddleware = async (req, res, next) => {
  await new Promise(resolve => setTimeout(resolve, 1000));
  next();
};
app.get('/login', async (req, res) => {
  if (req.session.userId) {
    return res.redirect('/dashboard');
  }
  try {
    const usersExist = await checkIfUsersExist();
    if (!usersExist) {
      return res.redirect('/setup-account');
    }
    res.render('login', {
      title: 'Login',
      error: null
    });
  } catch (error) {
    console.error('Error checking for users:', error);
    res.render('login', {
      title: 'Login',
      error: 'System error. Please try again.'
    });
  }
});

// Terms of Service page
app.get('/tos', (req, res) => {
  res.render('tos', {
    title: 'Terms of Service',
    active: 'tos'
  });
});

// Privacy Policy page
app.get('/privacy-policy', (req, res) => {
  res.render('privacy-policy', {
    title: 'Privacy Policy',
    active: 'privacy'
  });
});

// Regular user registration
app.get('/register', async (req, res) => {
  if (req.session.userId) {
    return res.redirect('/dashboard');
  }
  try {
    const usersExist = await checkIfUsersExist();
    if (!usersExist) {
      return res.redirect('/setup-account');
    }
    res.render('register', {
      title: 'Register',
      error: null
    });
  } catch (error) {
    console.error('Error on register page:', error);
    res.render('register', {
      title: 'Register',
      error: 'System error. Please try again.'
    });
  }
});
app.post('/login', loginDelayMiddleware, loginLimiter, async (req, res) => {
  const { username, password } = req.body;

  // Debug logging for login attempts
  if (!isProduction && enableConsoleLogging) {
    console.log('🔐 Login attempt:', {
      username,
      host: req.get('host'),
      userAgent: req.get('user-agent'),
      sessionId: req.sessionID,
      secure: req.session?.cookie?.secure
    });
  }

  try {
    const user = await User.findByUsername(username);
    if (!user) {
      if (!isProduction && enableConsoleLogging) {
        // console.log(`❌ Login failed: User '${username}' not found`); // Removed for production
      }
      return res.render('login', {
        title: 'Login',
        error: 'Invalid username or password'
      });
    }

    // Check if user account is active
    if (!user.is_active) {
      return res.render('login', {
        title: 'Login',
        error: 'Your account has been suspended. Please contact support.'
      });
    }

    const passwordMatch = await User.verifyPassword(password, user.password);
    if (!passwordMatch) {
      return res.render('login', {
        title: 'Login',
        error: 'Invalid username or password'
      });
    }
    req.session.userId = user.id;
    req.session.username = user.username;
    req.session.userRole = user.role || 'user';

    // Debug successful login
    if (!isProduction && enableConsoleLogging) {
      console.log(`✅ Login successful for user '${username}':`, {
        userId: user.id,
        sessionId: req.sessionID,
        cookieSecure: req.session.cookie.secure,
        host: req.get('host'),
        sessionData: {
          userId: req.session.userId,
          username: req.session.username,
          userRole: req.session.userRole
        }
      });
    }

    if (!isProduction && enableConsoleLogging) {
      // console.log('🔄 Redirecting to dashboard after successful login'); // Removed for production
    }

    // Force session save and redirect without URL parameters
    req.session.save((err) => {
      if (err) {
        console.error('❌ Session save error:', err);
        return res.render('login', {
          title: 'Login',
          error: 'Session error. Please try again.'
        });
      }

      if (!isProduction && enableConsoleLogging) {
        // console.log('✅ Session saved successfully, redirecting...'); // Removed for production
      }

      // Clean redirect without auth token in URL
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.redirect('/dashboard');
    });
  } catch (error) {
    console.error('Login error:', error);
    res.render('login', {
      title: 'Login',
      error: 'An error occurred during login. Please try again.'
    });
  }
});

// Regular user registration POST
app.post('/register', [
  body('username')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please enter a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
    .matches(/[0-9]/).withMessage('Password must contain at least one number'),
  body('confirmPassword')
    .custom((value, { req }) => value === req.body.password)
    .withMessage('Passwords do not match')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('register', {
        title: 'Register',
        user: { username: req.body.username || '', email: req.body.email || '' },
        error: errors.array()[0].msg
      });
    }

    // Check if username already exists
    const existingUsername = await User.findByUsername(req.body.username);
    if (existingUsername) {
      return res.render('register', {
        title: 'Register',
        user: { email: req.body.email || '' },
        error: 'Username is already taken'
      });
    }

    // Check if email already exists (if provided)
    if (req.body.email) {
      const existingEmail = await User.findByEmail(req.body.email);
      if (existingEmail) {
        return res.render('register', {
          title: 'Register',
          user: { username: req.body.username || '' },
          error: 'Email is already registered'
        });
      }
    }

    // Avatar upload removed - users can only upload profile pictures after login

    // Get Preview plan details
    const Subscription = require('./models/Subscription');
    const previewPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = ? AND is_active = 1', ['Preview'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!previewPlan) {
      return res.render('register', {
        title: 'Register',
        user: { username: req.body.username || '', email: req.body.email || '' },
        error: 'Preview plan not found. Please contact support.'
      });
    }

    const isProduction = process.env.NODE_ENV === 'production';
    if (!isProduction) {
      // console.debug('Preview plan found:', previewPlan); // Removed for production
    }

    // Create new user with correct Preview plan limits
    const userId = uuidv4();
    await User.create({
      id: userId,
      username: req.body.username,
      email: req.body.email,
      password: req.body.password,
      avatar_path: null, // No avatar during registration
      role: 'user',
      plan_type: 'Preview',
      max_streaming_slots: previewPlan.max_streaming_slots,
      max_storage_gb: previewPlan.max_storage_gb
    });

    // Create automatic subscription for Preview plan
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 10); // Preview plan never expires

    await Subscription.createSubscription({
      user_id: userId,
      plan_id: previewPlan.id,
      status: 'active',
      end_date: endDate.toISOString(),
      payment_method: 'free'
    });

    // Set session
    req.session.userId = userId;
    req.session.username = req.body.username;
    req.session.userRole = 'user';
    // No avatar path set during registration - users can upload avatars after login

    // Send notification for new user registration (admin notification)
    try {
      await notificationService.notifyUserRegistered(userId, req.body.username, req.body.email || 'No email provided');
    } catch (notifError) {
      console.error('Error sending user registration notification:', notifError);
    }

    // Send welcome notification to the new user
    try {
      await notificationService.notifyUserWelcome(userId, req.body.username);
    } catch (notifError) {
      console.error('Error sending welcome notification:', notifError);
    }

    res.redirect('/dashboard');
  } catch (error) {
    console.error('Registration error:', error);
    res.render('register', {
      title: 'Register',
      user: { username: req.body.username || '', email: req.body.email || '' },
      error: 'An error occurred during registration. Please try again.'
    });
  }
});
app.get('/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/login');
});
app.get('/setup-account', async (req, res) => {
  try {
    const usersExist = await checkIfUsersExist();
    if (usersExist && !req.session.userId) {
      return res.redirect('/login');
    }
    if (req.session.userId) {
      const user = await User.findById(req.session.userId);
      if (user && user.username) {
        return res.redirect('/dashboard');
      }
    }
    res.render('setup-account', {
      title: 'Complete Your Account',
      user: req.session.userId ? await User.findById(req.session.userId) : {},
      error: null
    });
  } catch (error) {
    console.error('Setup account error:', error);
    res.redirect('/login');
  }
});
app.post('/setup-account', upload.single('avatar'), [
  body('username')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please enter a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
    .matches(/[0-9]/).withMessage('Password must contain at least one number'),
  body('confirmPassword')
    .custom((value, { req }) => value === req.body.password)
    .withMessage('Passwords do not match')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      return res.render('setup-account', {
        title: 'Complete Your Account',
        user: { username: req.body.username || '' },
        error: errors.array()[0].msg
      });
    }
    const existingUsername = await User.findByUsername(req.body.username);
    if (existingUsername) {
      return res.render('setup-account', {
        title: 'Complete Your Account',
        user: { email: req.body.email || '' },
        error: 'Username is already taken'
      });
    }

    // Check if email is already taken (if provided)
    if (req.body.email) {
      const existingEmail = await User.findByEmail(req.body.email);
      if (existingEmail) {
        return res.render('setup-account', {
          title: 'Complete Your Account',
          user: { username: req.body.username || '' },
          error: 'Email is already taken'
        });
      }
    }
    const avatarPath = req.file ? `/uploads/avatars/${req.file.filename}` : null;
    const usersExist = await checkIfUsersExist();
    if (!usersExist) {
      try {
        const userId = uuidv4();
        await User.create({
          id: userId,
          username: req.body.username,
          email: req.body.email,
          password: req.body.password,
          avatar_path: avatarPath,
          role: 'admin', // First user is admin
          max_streaming_slots: -1, // Unlimited for admin
          max_storage_gb: 1000 // 1TB for admin
        });
        req.session.userId = userId;
        req.session.username = req.body.username;
        req.session.userRole = 'admin';
        if (avatarPath) {
          req.session.avatar_path = avatarPath;
        }
        return res.redirect('/dashboard');
      } catch (error) {
        console.error('User creation error:', error);
        return res.render('setup-account', {
          title: 'Complete Your Account',
          user: {},
          error: 'Failed to create user. Please try again.'
        });
      }
    } else {
      await User.update(req.session.userId, {
        username: req.body.username,
        password: req.body.password,
        avatar_path: avatarPath,
      });
      req.session.username = req.body.username;
      if (avatarPath) {
        req.session.avatar_path = avatarPath;
      }
      res.redirect('/dashboard');
    }
  } catch (error) {
    console.error('Account setup error:', error);
    res.render('setup-account', {
      title: 'Complete Your Account',
      user: { email: req.body.email || '' },
      error: 'An error occurred. Please try again.'
    });
  }
});
// Mount routes
app.use('/admin', adminRoutes);
app.use('/subscription', subscriptionRoutes);
app.use('/payment', require('./routes/payment'));
app.use('/api/notifications', notificationRoutes);

// SEO routes
app.use('/', require('./routes/seo'));

// Test route for Midtrans
app.get('/test-midtrans', isAuthenticated, (req, res) => {
  res.render('test-midtrans', {
    title: 'Test Midtrans Integration',
    midtransClientKey: process.env.MIDTRANS_CLIENT_KEY
  });
});

// Language switching route
app.post('/api/language', (req, res) => {
  const { language } = req.body;

  if (!language || !['en', 'id'].includes(language)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid language code'
    });
  }

  // Save to session
  if (req.session) {
    req.session.locale = language;
  }

  // Save to cookie
  res.cookie('lang', language, {
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production'
  });

  res.json({
    success: true,
    language: language,
    message: 'Language updated successfully'
  });
});

// Test i18n route
app.get('/test-i18n', (req, res) => {
  res.json({
    locale: res.locals.locale,
    test_translation: res.locals.t('history.title'),
    available_locales: i18n.getLocales()
  });
});

// Import enhanced error handling
const { errorHandlerMiddleware, AppError, ERROR_TYPES } = require('./utils/errorHandler');

// Global error handler middleware
app.use((error, req, res, next) => {
  // Track error in performance monitor
  if (performanceMonitor && performanceMonitor.trackError) {
    performanceMonitor.trackError(error);
  }

  // Use enhanced error handler for API requests
  if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
    return errorHandlerMiddleware(error, req, res, next);
  }

  // Web request - render error page with enhanced error handling
  const isDevelopment = process.env.NODE_ENV !== 'production';

  // Convert to AppError if needed for consistent handling
  if (!(error instanceof AppError)) {
    error = new AppError(
      error.message || 'An unexpected error occurred',
      ERROR_TYPES.INTERNAL,
      error.statusCode || 500
    );
  }

  res.status(error.statusCode || 500).render('error', {
    title: 'Error',
    message: error.message || 'Something went wrong',
    error: isDevelopment ? error : {},
    errorId: error.errorId
  });
});

// Landing page route
app.get('/', async (req, res) => {
  // If user is already logged in, redirect to dashboard
  if (req.session.userId) {
    return res.redirect('/dashboard');
  }

  let landingPlans = [];

  try {
    // Get subscription plans for pricing section
    const plans = await Subscription.getAllPlans();
    const isProduction = process.env.NODE_ENV === 'production';
    if (!isProduction) {
      // console.debug('📋 Loaded plans for landing page:', plans.length); // Removed for production
    }

    // Show all active plans for landing page (no filtering by specific names)
    landingPlans = plans.filter(plan => plan.is_active !== 0)
      .sort((a, b) => a.price - b.price); // Sort by price ascending

    if (!isProduction) {
      console.debug('📋 Landing plans:', landingPlans.map(p => `${p.name}: ${p.currency} ${p.price}`));
    }

  } catch (error) {
    console.error('❌ Error loading subscription plans for landing page:', error);
    // Use fallback static data - empty array if database fails
    landingPlans = [];
    // console.log('📋 Using empty fallback for landing page due to database error'); // Removed for production
  }



  // Render landing page
  res.render('landing', {
    title: 'StreamOnPod - 24/7 Live Streaming Platform',
    active: 'landing',
    plans: landingPlans
  });
});
app.get('/dashboard', async (req, res) => {
  try {
    const userId = req.session.userId;

    if (!userId) {
      return res.redirect('/login');
    }

    if (!isProduction && enableConsoleLogging) {
      // console.log('📊 Dashboard accessed by user:', userId); // Removed for production
    }
    const user = await User.findById(userId);
    if (!isProduction && enableConsoleLogging) {
      // console.log('👤 User found:', user ? user.username : 'null'); // Removed for production
    }

    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(userId);
    if (!isProduction && enableConsoleLogging) {
      // console.log('📈 Quota info:', quotaInfo); // Removed for production
    }

    const userStats = await User.getUserStats(userId);
    if (!isProduction && enableConsoleLogging) {
      // console.log('📊 User stats:', userStats); // Removed for production
      // console.log('🎨 Rendering dashboard...'); // Removed for production
    }
    res.render('dashboard', {
      title: 'Dashboard',
      active: 'dashboard',
      user: user,
      quota: quotaInfo,
      stats: userStats
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.redirect('/login');
  }
});
app.get('/gallery', isAuthenticated, async (req, res) => {
  try {
    const videos = await Video.findAll(req.session.userId);
    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);
    res.render('gallery', {
      title: 'Video Gallery',
      active: 'gallery',
      user: await User.findById(req.session.userId),
      videos: videos,
      quota: quotaInfo
    });
  } catch (error) {
    console.error('Gallery error:', error);
    res.redirect('/dashboard');
  }
});
app.get('/settings', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.session.userId);
    if (!user) {
      req.session.destroy();
      return res.redirect('/login');
    }
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: user
    });
  } catch (error) {
    console.error('Settings error:', error);
    res.redirect('/login');
  }
});
app.get('/history', isAuthenticated, async (req, res) => {
  try {
    const db = require('./db/database').db;
    const history = await new Promise((resolve, reject) => {
      db.all(
        `SELECT h.*, v.thumbnail_path
         FROM stream_history h
         LEFT JOIN videos v ON h.video_id = v.id
         WHERE h.user_id = ?
         ORDER BY h.start_time DESC`,
        [req.session.userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
    res.render('history', {
      active: 'history',
      title: res.locals.t('history.title'),
      history: history,
      helpers: app.locals.helpers
    });
  } catch (error) {
    console.error('Error fetching stream history:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load stream history',
      error: error
    });
  }
});
app.delete('/api/history/:id', isAuthenticated, async (req, res) => {
  try {
    const db = require('./db/database').db;
    const historyId = req.params.id;
    const history = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM stream_history WHERE id = ? AND user_id = ?',
        [historyId, req.session.userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    if (!history) {
      return res.status(404).json({
        success: false,
        error: 'History entry not found or not authorized'
      });
    }
    await new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM stream_history WHERE id = ?',
        [historyId],
        function (err) {
          if (err) reject(err);
          else resolve(this);
        }
      );
    });
    res.json({ success: true, message: 'History entry deleted' });
  } catch (error) {
    console.error('Error deleting history entry:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete history entry'
    });
  }
});
app.get('/api/system-stats', isAuthenticated, async (req, res) => {
  try {
    const stats = await systemMonitor.getSystemStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Performance monitoring API endpoints
app.get('/api/performance/summary', isAuthenticated, (req, res) => {
  try {
    const summary = performanceMonitor.getPerformanceSummary();
    res.json(summary);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/performance/detailed', isAuthenticated, (req, res) => {
  try {
    const report = performanceMonitor.getDetailedReport();
    res.json(report);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/cache/stats', isAuthenticated, (req, res) => {
  try {
    const stats = cacheService.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/cache/clear', isAuthenticated, (req, res) => {
  try {
    cacheService.clear();
    res.json({ success: true, message: 'Cache cleared successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/database/stats', isAuthenticated, async (req, res) => {
  try {
    const stats = await dbOptimizer.getDatabaseStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Load balancing API endpoints
app.get('/api/load-balancer/status', isAuthenticated, (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    const status = loadBalancer.getStatus();
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/load-balancer/metrics', isAuthenticated, (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    const metrics = loadBalancer.getMetrics();
    res.json(metrics);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-balancer/config', isAuthenticated, async (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    const result = loadBalancer.updateConfig(req.body);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-balancer/quality', isAuthenticated, async (req, res) => {
  try {
    const { qualityLevel } = req.body;
    if (!qualityLevel) {
      return res.status(400).json({ error: 'Quality level is required' });
    }

    const loadBalancer = require('./services/loadBalancer');
    const result = await loadBalancer.setQualityLevel(qualityLevel);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-balancer/start', isAuthenticated, (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    loadBalancer.start();
    res.json({ success: true, message: 'Load balancer started' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-balancer/stop', isAuthenticated, (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    loadBalancer.stop();
    res.json({ success: true, message: 'Load balancer stopped' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// MKV Optimizer API endpoints
app.get('/api/mkv-optimizer/status', isAuthenticated, (req, res) => {
  try {
    const mkvOptimizer = require('./services/mkvOptimizer');
    const status = mkvOptimizer.getStatus();
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/mkv-optimizer/config', isAuthenticated, (req, res) => {
  try {
    const mkvOptimizer = require('./services/mkvOptimizer');
    const result = mkvOptimizer.updateConfig(req.body);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/mkv-optimizer/can-stream', isAuthenticated, async (req, res) => {
  try {
    const mkvOptimizer = require('./services/mkvOptimizer');
    const result = await mkvOptimizer.canStreamMkv();
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Note: /subscription/quota endpoint is handled by routes/subscription.js

// Get user profile information (for role checking)
app.get('/api/user/profile', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.session.userId);
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        plan_type: user.plan_type
      }
    });
  } catch (error) {
    console.error('Error getting user profile:', error);
    res.status(500).json({ success: false, error: 'Failed to get user profile' });
  }
});
function getLocalIpAddresses() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  Object.keys(interfaces).forEach((ifname) => {
    interfaces[ifname].forEach((iface) => {
      if (iface.family === 'IPv4' && !iface.internal) {
        addresses.push(iface.address);
      }
    });
  });
  return addresses.length > 0 ? addresses : ['localhost'];
}
app.post('/settings/profile', isAuthenticated, upload.single('avatar'), [
  body('username')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('settings', {
        title: 'Settings',
        active: 'settings',
        user: await User.findById(req.session.userId),
        error: errors.array()[0].msg,
        activeTab: 'profile'
      });
    }
    const currentUser = await User.findById(req.session.userId);
    if (req.body.username !== currentUser.username) {
      const existingUser = await User.findByUsername(req.body.username);
      if (existingUser) {
        return res.render('settings', {
          title: 'Settings',
          active: 'settings',
          user: currentUser,
          error: 'Username is already taken',
          activeTab: 'profile'
        });
      }
    }
    const updateData = {
      username: req.body.username
    };
    if (req.file) {
      updateData.avatar_path = `/uploads/avatars/${req.file.filename}`;
    }
    await User.update(req.session.userId, updateData);
    req.session.username = updateData.username;
    if (updateData.avatar_path) {
      req.session.avatar_path = updateData.avatar_path;
    }
    return res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      success: 'Profile updated successfully!',
      activeTab: 'profile'
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      error: 'An error occurred while updating your profile',
      activeTab: 'profile'
    });
  }
});
app.post('/settings/password', isAuthenticated, [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
    .matches(/[0-9]/).withMessage('Password must contain at least one number'),
  body('confirmPassword')
    .custom((value, { req }) => value === req.body.newPassword)
    .withMessage('Passwords do not match'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('settings', {
        title: 'Settings',
        active: 'settings',
        user: await User.findById(req.session.userId),
        error: errors.array()[0].msg,
        activeTab: 'security'
      });
    }
    const user = await User.findById(req.session.userId);
    const passwordMatch = await User.verifyPassword(req.body.currentPassword, user.password);
    if (!passwordMatch) {
      return res.render('settings', {
        title: 'Settings',
        active: 'settings',
        user: user,
        error: 'Current password is incorrect',
        activeTab: 'security'
      });
    }
    const hashedPassword = await bcrypt.hash(req.body.newPassword, 10);
    await User.update(req.session.userId, { password: hashedPassword });
    return res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      success: 'Password changed successfully',
      activeTab: 'security'
    });
  } catch (error) {
    console.error('Error changing password:', error);
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      error: 'An error occurred while changing your password',
      activeTab: 'security'
    });
  }
});
app.get('/settings', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.session.userId);
    if (!user) {
      req.session.destroy();
      return res.redirect('/login');
    }
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: user
    });
  } catch (error) {
    console.error('Settings error:', error);
    res.redirect('/dashboard');
  }
});
app.post('/settings/integrations/gdrive', isAuthenticated, [
  body('apiKey').notEmpty().withMessage('API Key is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('settings', {
        title: 'Settings',
        active: 'settings',
        user: await User.findById(req.session.userId),
        error: errors.array()[0].msg,
        activeTab: 'integrations'
      });
    }
    await User.update(req.session.userId, {
      gdrive_api_key: req.body.apiKey
    });
    return res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      success: 'Google Drive API key saved successfully!',
      activeTab: 'integrations'
    });
  } catch (error) {
    console.error('Error saving Google Drive API key:', error);
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      error: 'An error occurred while saving your Google Drive API key',
      activeTab: 'integrations'
    });
  }
});
app.post('/upload/video', isAuthenticated, uploadVideo.single('video'), QuotaMiddleware.checkStorageQuota(), QuotaMiddleware.updateStorageUsage(), async (req, res) => {
  try {
    const isProduction = process.env.NODE_ENV === 'production';
    if (!isProduction) {
      // console.debug('Upload request received:', req.file); // Removed for production
    }

    if (!req.file) {
      return res.status(400).json({ error: 'No video file provided' });
    }
    const { filename, originalname, path: videoPath, mimetype, size } = req.file;
    const thumbnailName = path.basename(filename, path.extname(filename)) + '.jpg';
    const videoInfo = await getVideoInfo(videoPath);
    const thumbnailRelativePath = await generateThumbnail(videoPath, thumbnailName)
      .then(() => `/uploads/thumbnails/${thumbnailName}`)
      .catch(() => null);
    let format = 'unknown';
    if (mimetype === 'video/mp4') format = 'mp4';
    else if (mimetype === 'video/avi') format = 'avi';
    else if (mimetype === 'video/quicktime') format = 'mov';
    else if (mimetype === 'video/x-matroska') format = 'mkv';
    else if (originalname.toLowerCase().endsWith('.mkv')) format = 'mkv';
    const videoData = {
      title: path.basename(originalname, path.extname(originalname)),
      original_filename: originalname,
      filepath: `/uploads/videos/${filename}`,
      thumbnail_path: thumbnailRelativePath,
      file_size: size,
      duration: videoInfo.duration,
      format: format,
      user_id: req.session.userId
    };
    const video = await Video.create(videoData);
    res.json({
      success: true,
      video: {
        id: video.id,
        title: video.title,
        filepath: video.filepath,
        thumbnail_path: video.thumbnail_path,
        duration: video.duration,
        file_size: video.file_size,
        format: video.format
      }
    });
  } catch (error) {
    console.error('Upload error details:', error);
    res.status(500).json({
      error: 'Failed to upload video',
      details: error.message
    });
  }
});
// Error handling middleware for multer
const handleMulterError = (err, req, res, next) => {
  // console.log('🔍 Upload error details:', {
  //   error: err && err.message,
  //   code: err && err.code,
  //   type: err && err.constructor && err.constructor.name,
  //   fileSize: req.file && req.file.size,
  //   maxSize: process.env.MAX_FILE_SIZE
  // }); // Removed for production
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      const maxSizeMB = Math.round((parseInt(process.env.MAX_FILE_SIZE) || 2147483648) / (1024 * 1024));
      // console.log(`❌ File too large: ${(req.file && req.file.size) || 'unknown'} bytes > ${process.env.MAX_FILE_SIZE} bytes`); // Removed for production
      return res.status(413).json({
        error: 'File too large',
        message: `File size exceeds the maximum limit of ${maxSizeMB}MB. Please choose a smaller file.`,
        details: {
          fileSize: req.file && req.file.size,
          maxSize: parseInt(process.env.MAX_FILE_SIZE) || 2147483648,
          maxSizeMB: maxSizeMB
        }
      });
    } else if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        error: 'Too many files',
        message: 'Only one file can be uploaded at a time.'
      });
    } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        error: 'Invalid field name',
        message: 'The file field name is invalid.'
      });
    }
  } else if (err) {
    // console.log(`❌ General upload error: ${err.message}`); // Removed for production
    return res.status(400).json({
      error: 'Upload error',
      message: err.message || 'An error occurred during file upload.'
    });
  }
  next();
};

app.post('/api/videos/upload', isAuthenticated, (req, res, next) => {
  // console.log('🔍 Upload request received:', {
  //   contentLength: req.headers['content-length'],
  //   contentType: req.headers['content-type'],
  //   userAgent: req.headers['user-agent']
  // }); // Removed for production
  next();
}, videoUpload.single('video'), handleMulterError, QuotaMiddleware.checkStorageQuota(), async (req, res) => {
  const startTime = Date.now();
  if (!isProduction && enableConsoleLogging) {
    console.log(`📤 Upload started for user ${req.session.userId} at ${new Date().toISOString()}`);
  }

  try {
    if (!isProduction && enableConsoleLogging) {
      // console.debug('Upload request received:', req.file); // Removed for production
    }

    if (!req.file) {
      if (!isProduction && enableConsoleLogging) {
        // console.log('❌ Upload failed: No file uploaded'); // Removed for production
      }
      return res.status(400).json({ error: 'No video file provided' });
    }

    const fileSizeMB = (req.file.size / (1024 * 1024)).toFixed(2);
    if (!isProduction && enableConsoleLogging) {
      console.log(`📁 File received: ${req.file.originalname} (${fileSizeMB}MB)`);
    }
    let title = path.parse(req.file.originalname).name;
    const filePath = `/uploads/videos/${req.file.filename}`;
    const fullFilePath = path.join(__dirname, 'public', filePath);
    const fileSize = req.file.size;
    await new Promise((resolve, reject) => {
      ffmpeg.ffprobe(fullFilePath, (err, metadata) => {
        if (err) {
          console.error('Error extracting metadata:', err);
          return reject(err);
        }
        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
        const duration = metadata.format.duration || 0;
        const format = metadata.format.format_name || '';
        const resolution = videoStream ? `${videoStream.width}x${videoStream.height}` : '';
        const bitrate = metadata.format.bit_rate ?
          Math.round(parseInt(metadata.format.bit_rate) / 1000) :
          null;
        let fps = null;
        if (videoStream && videoStream.avg_frame_rate) {
          const fpsRatio = videoStream.avg_frame_rate.split('/');
          if (fpsRatio.length === 2 && parseInt(fpsRatio[1]) !== 0) {
            fps = Math.round((parseInt(fpsRatio[0]) / parseInt(fpsRatio[1]) * 100)) / 100;
          } else {
            fps = parseInt(fpsRatio[0]) || null;
          }
        }

        // Extract codec information
        const codec = videoStream ? videoStream.codec_name : null;
        const audioCodec = audioStream ? audioStream.codec_name : null;

        // console.log(`[Upload] Video codec info: ${codec}, Audio codec: ${audioCodec}`); // Removed for production
        // Generate thumbnail
        const thumbnailFilename = `thumb-${path.parse(req.file.filename).name}.jpg`;
        const thumbnailPath = `/uploads/thumbnails/${thumbnailFilename}`;
        const fullThumbnailPath = path.join(__dirname, 'public', thumbnailPath);

        // console.log(`🖼️ Starting thumbnail generation:`); // Removed for production
        // console.log(`   Source: ${fullFilePath}`); // Removed for production
        // console.log(`   Output: ${fullThumbnailPath}`); // Removed for production
        // console.log(`   Duration: ${duration}s`); // Removed for production
        // Ensure thumbnails directory exists
        const thumbnailDir = path.join(__dirname, 'public', 'uploads', 'thumbnails');
        if (!fs.existsSync(thumbnailDir)) {
          fs.mkdirSync(thumbnailDir, { recursive: true });
          // console.log(`📁 Created thumbnails directory: ${thumbnailDir}`); // Removed for production
        }

        // Choose appropriate seek time based on video duration
        const seekTime = duration > 10 ? '00:00:10' : Math.max(1, Math.floor(duration / 2));
        // console.log(`⏰ Using seek time: ${seekTime}`); // Removed for production
        ffmpeg(fullFilePath)
          .inputOptions([
            '-hwaccel', 'none',
            '-ss', seekTime.toString()
          ])
          .outputOptions([
            '-vframes', '1',
            '-q:v', '2',
            '-vf', 'scale=854:480:force_original_aspect_ratio=decrease,pad=854:480:(ow-iw)/2:(oh-ih)/2'
          ])
          .output(fullThumbnailPath)
          .on('start', (commandLine) => {
            // console.log(`🎬 FFmpeg command: ${commandLine}`); // Removed for production
          })
          .on('end', async () => {
            // console.log(`✅ Thumbnail generated successfully: ${thumbnailFilename}`); // Removed for production
            // Verify thumbnail file exists
            if (fs.existsSync(fullThumbnailPath)) {
              const stats = fs.statSync(fullThumbnailPath);
              // console.log(`📊 Thumbnail size: ${stats.size} bytes`); // Removed for production
            } else {
              console.error(`❌ Thumbnail file not found after generation: ${fullThumbnailPath}`);
            }
            try {
              const videoData = {
                title,
                filepath: filePath,
                thumbnail_path: thumbnailPath,
                file_size: fileSize,
                duration,
                format,
                resolution,
                bitrate,
                fps,
                codec,
                audioCodec,
                user_id: req.session.userId
              };
              const video = await Video.create(videoData);

              // Add video to processing queue for streaming-ready conversion
              try {
                await videoProcessingService.addToQueue(video.id);
                console.log(`[Upload] Video ${video.id} added to processing queue`);
              } catch (processingError) {
                console.error('[Upload] Error adding video to processing queue:', processingError);
                // Don't fail the upload, just log the error
              }

              // Update storage usage after successful upload
              if (req.uploadSizeGB && req.session.userId) {
                if (!isProduction) {
                  console.debug(`📊 Updating storage: +${req.uploadSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
                }
                await Subscription.updateStorageUsage(req.session.userId, req.uploadSizeGB);
                if (!isProduction) {
                  // console.debug('✅ Storage updated successfully'); // Removed for production
                }
              }

              const uploadTime = Date.now() - startTime;
              console.log(`✅ Upload completed successfully in ${(uploadTime / 1000).toFixed(2)}s`);
              console.log(`📊 Video saved: ${video.title} (ID: ${video.id})`);

              res.json({
                success: true,
                message: 'Video uploaded successfully',
                video
              });
              resolve();
            } catch (dbError) {
              console.error('Database error:', dbError);

              // Clean up uploaded files on database error
              try {
                if (fs.existsSync(fullFilePath)) {
                  fs.unlinkSync(fullFilePath);
                  console.log(`🗑️ Cleaned up video file due to database error: ${fullFilePath}`);
                }
                if (fs.existsSync(fullThumbnailPath)) {
                  fs.unlinkSync(fullThumbnailPath);
                  console.log(`🗑️ Cleaned up thumbnail file due to database error: ${fullThumbnailPath}`);
                }
              } catch (cleanupError) {
                console.error('❌ Error cleaning up files after database error:', cleanupError.message);
              }

              reject(dbError);
            }
          })
          .on('error', (err) => {
            console.error('❌ Error creating thumbnail:', err);
            console.error('   Error details:', {
              message: err.message,
              code: err.code,
              signal: err.signal
            });

            // Try to create a fallback thumbnail or continue without thumbnail
            // console.log('🔄 Attempting to save video without thumbnail...'); // Removed for production
            // Try alternative thumbnail generation with simpler settings
            // console.log('🔄 Trying alternative thumbnail generation...'); // Removed for production
            const fallbackThumbnailPath = fullThumbnailPath.replace('.jpg', '_fallback.jpg');

            ffmpeg(fullFilePath)
              .inputOptions(['-ss', '1'])
              .outputOptions([
                '-vframes', '1',
                '-q:v', '5',
                '-vf', 'scale=640:360'
              ])
              .output(fallbackThumbnailPath)
              .on('end', async () => {
                // console.log('✅ Fallback thumbnail generated successfully'); // Removed for production
                // Save video with fallback thumbnail
                const videoDataWithFallback = {
                  title,
                  filepath: filePath,
                  thumbnail_path: thumbnailPath.replace('.jpg', '_fallback.jpg'),
                  file_size: fileSize,
                  duration,
                  format,
                  resolution,
                  bitrate,
                  fps,
                  codec,
                  audioCodec,
                  user_id: req.session.userId
                };

                try {
                  const video = await Video.create(videoDataWithFallback);

                  // Update storage usage
                  if (req.uploadSizeGB && req.session.userId) {
                    if (!isProduction) {
                      console.debug(`📊 Updating storage: +${req.uploadSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
                    }
                    await Subscription.updateStorageUsage(req.session.userId, req.uploadSizeGB);
                    if (!isProduction) {
                      // console.debug('✅ Storage updated successfully'); // Removed for production
                    }
                  }

                  const uploadTime = Date.now() - startTime;
                  console.log(`✅ Upload completed successfully in ${(uploadTime / 1000).toFixed(2)}s (with fallback thumbnail)`);
                  console.log(`📊 Video saved: ${video.title} (ID: ${video.id})`);

                  res.json({
                    success: true,
                    message: 'Video uploaded successfully',
                    video
                  });
                  resolve();
                } catch (dbError) {
                  console.error('Database error with fallback thumbnail:', dbError);
                  // Continue with no thumbnail approach
                  proceedWithoutThumbnail();
                }
              })
              .on('error', (fallbackErr) => {
                console.error('❌ Fallback thumbnail generation also failed:', fallbackErr);

                // Clean up fallback thumbnail file if it exists
                try {
                  if (fs.existsSync(fallbackThumbnailPath)) {
                    fs.unlinkSync(fallbackThumbnailPath);
                    console.log(`🗑️ Cleaned up fallback thumbnail file: ${fallbackThumbnailPath}`);
                  }
                } catch (cleanupError) {
                  console.error('❌ Error cleaning up fallback thumbnail:', cleanupError.message);
                }

                proceedWithoutThumbnail();
              })
              .run();

            function proceedWithoutThumbnail() {
              // console.log('💾 Saving video without thumbnail...'); // Removed for production
              // Save video data without thumbnail
              const videoDataFallback = {
                title,
                filepath: filePath,
                thumbnail_path: null, // No thumbnail
                file_size: fileSize,
                duration,
                format,
                resolution,
                bitrate,
                fps,
                codec,
                audioCodec,
                user_id: req.session.userId
              };

              Video.create(videoDataFallback)
                .then(async (video) => {
                  // Update storage usage after successful upload
                  if (req.uploadSizeGB && req.session.userId) {
                    if (!isProduction) {
                      console.debug(`📊 Updating storage: +${req.uploadSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
                    }
                    await Subscription.updateStorageUsage(req.session.userId, req.uploadSizeGB);
                    if (!isProduction) {
                      // console.debug('✅ Storage updated successfully'); // Removed for production
                    }
                  }

                  const uploadTime = Date.now() - startTime;
                  console.log(`✅ Upload completed successfully in ${(uploadTime / 1000).toFixed(2)}s (without thumbnail)`);
                  console.log(`📊 Video saved: ${video.title} (ID: ${video.id})`);

                  res.json({
                    success: true,
                    message: 'Video uploaded successfully (thumbnail generation failed)',
                    video
                  });
                  resolve();
                })
                .catch((dbError) => {
                  console.error('Database error in fallback:', dbError);

                  // Clean up uploaded video file on database error
                  try {
                    if (fs.existsSync(fullFilePath)) {
                      fs.unlinkSync(fullFilePath);
                      console.log(`🗑️ Cleaned up video file due to database error in fallback: ${fullFilePath}`);
                    }
                  } catch (cleanupError) {
                    console.error('❌ Error cleaning up video file after database error in fallback:', cleanupError.message);
                  }

                  reject(dbError);
                });
            }
          })
          .run();
      });
    });
  } catch (error) {
    console.error('Upload error details:', error);

    // Clean up uploaded file on error
    if (req.file && req.file.path) {
      try {
        if (fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
          console.log(`🗑️ Cleaned up uploaded file due to error: ${req.file.path}`);
        }
      } catch (cleanupError) {
        console.error('❌ Error cleaning up uploaded file:', cleanupError.message);
      }
    }

    res.status(500).json({
      error: 'Failed to upload video',
      details: error.message
    });
  }
});

// ===== CHUNKED UPLOAD ROUTES =====

// Initialize chunked upload
app.post('/api/videos/upload/init', isAuthenticated, async (req, res) => {
  try {
    const { filename, fileSize, totalChunks } = req.body;

    // Validate inputs
    if (!filename || !fileSize || !totalChunks) {
      return res.status(400).json({
        error: 'Missing required parameters',
        details: 'filename, fileSize, and totalChunks are required'
      });
    }

    // Check storage quota before initializing upload
    const fileSizeGB = fileSize / (1024 * 1024 * 1024);
    const isAdmin = await Permission.isAdmin(req.session.userId);

    if (!isAdmin) {
      const quotaCheck = await Subscription.checkStorageLimit(req.session.userId, fileSizeGB);
      if (quotaCheck.hasLimit) {
        return res.status(403).json({
          error: 'Storage quota exceeded',
          details: quotaCheck
        });
      }
    }

    // Initialize upload
    const uploadInfo = await chunkedUploadService.initializeUpload(
      req.session.userId,
      filename,
      fileSize,
      totalChunks
    );

    res.json({
      success: true,
      uploadId: uploadInfo.uploadId,
      chunkSize: uploadInfo.chunkSize,
      finalFilename: uploadInfo.finalFilename
    });

  } catch (error) {
    console.error('Error initializing chunked upload:', error);
    res.status(500).json({
      error: 'Failed to initialize upload',
      details: error.message
    });
  }
});

// Upload individual chunk
app.post('/api/videos/upload/chunk', isAuthenticated, (req, res, next) => {
  console.log('🔍 Chunk upload request received:', {
    contentLength: req.headers['content-length'],
    contentType: req.headers['content-type'],
    body: req.body
  });
  next();
}, uploadChunk.single('chunk'), (error, req, res, next) => {
  // Handle multer errors specifically
  if (error) {
    console.error('Multer error in chunk upload:', error);
    return res.status(400).json({
      error: 'File upload error',
      details: error.message
    });
  }
  next();
}, async (req, res) => {
  try {
    console.log('📦 Processing chunk:', {
      uploadId: req.body.uploadId,
      chunkIndex: req.body.chunkIndex,
      hasFile: !!req.file,
      fileSize: req.file ? req.file.size : 0
    });

    const { uploadId, chunkIndex } = req.body;

    if (!uploadId || chunkIndex === undefined) {
      console.error('❌ Missing required parameters:', { uploadId, chunkIndex });
      return res.status(400).json({
        error: 'Missing required parameters',
        details: 'uploadId and chunkIndex are required'
      });
    }

    if (!req.file) {
      console.error('❌ No chunk file provided');
      return res.status(400).json({
        error: 'No chunk file provided'
      });
    }

    // Process the chunk
    const result = await chunkedUploadService.processChunk(
      uploadId,
      parseInt(chunkIndex),
      req.file.path
    );

    console.log('✅ Chunk processed successfully:', result);

    res.json({
      success: true,
      chunkIndex: result.chunkIndex,
      received: result.received,
      total: result.total,
      isComplete: result.isComplete
    });

  } catch (error) {
    console.error('❌ Error processing chunk:', error);
    res.status(500).json({
      error: 'Failed to process chunk',
      details: error.message
    });
  }
});

// Finalize chunked upload
app.post('/api/videos/upload/finalize', isAuthenticated, QuotaMiddleware.updateStorageUsage(), async (req, res) => {
  const startTime = Date.now();

  try {
    const { uploadId } = req.body;

    if (!uploadId) {
      return res.status(400).json({
        error: 'Upload ID is required'
      });
    }

    // Finalize upload (merge chunks)
    const finalizedFile = await chunkedUploadService.finalizeUpload(uploadId);

    // Process the final video file (same as regular upload)
    const filePath = `/uploads/videos/${finalizedFile.filename}`;
    const fullFilePath = finalizedFile.path;
    const fileSize = finalizedFile.size;
    let title = path.parse(finalizedFile.originalName).name;

    // Extract video metadata and generate thumbnail (same logic as regular upload)
    await new Promise((resolve, reject) => {
      ffmpeg.ffprobe(fullFilePath, (err, metadata) => {
        if (err) {
          console.error('Error extracting metadata:', err);
          return reject(err);
        }

        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
        const duration = metadata.format.duration || 0;
        const format = metadata.format.format_name || '';
        const resolution = videoStream ? `${videoStream.width}x${videoStream.height}` : '';
        const bitrate = metadata.format.bit_rate ? Math.round(parseInt(metadata.format.bit_rate) / 1000) : null;

        let fps = null;
        if (videoStream && videoStream.avg_frame_rate) {
          const fpsRatio = videoStream.avg_frame_rate.split('/');
          if (fpsRatio.length === 2 && parseInt(fpsRatio[1]) !== 0) {
            fps = Math.round((parseInt(fpsRatio[0]) / parseInt(fpsRatio[1]) * 100)) / 100;
          } else {
            fps = parseInt(fpsRatio[0]) || null;
          }
        }

        const codec = videoStream ? videoStream.codec_name : null;
        const audioCodec = audioStream ? audioStream.codec_name : null;

        // Generate thumbnail
        const thumbnailFilename = `thumb-${path.parse(finalizedFile.filename).name}.jpg`;
        const thumbnailPath = `/uploads/thumbnails/${thumbnailFilename}`;
        const fullThumbnailPath = path.join(__dirname, 'public', thumbnailPath);

        // Ensure thumbnails directory exists
        const thumbnailDir = path.join(__dirname, 'public', 'uploads', 'thumbnails');
        if (!fs.existsSync(thumbnailDir)) {
          fs.mkdirSync(thumbnailDir, { recursive: true });
        }

        const seekTime = duration > 10 ? '00:00:10' : Math.max(1, Math.floor(duration / 2));

        ffmpeg(fullFilePath)
          .inputOptions(['-hwaccel', 'none', '-ss', seekTime.toString()])
          .outputOptions([
            '-vframes', '1',
            '-q:v', '2',
            '-vf', 'scale=854:480:force_original_aspect_ratio=decrease,pad=854:480:(ow-iw)/2:(oh-ih)/2'
          ])
          .output(fullThumbnailPath)
          .on('end', async () => {
            try {
              const videoData = {
                title,
                filepath: filePath,
                thumbnail_path: thumbnailPath,
                file_size: fileSize,
                duration,
                format,
                resolution,
                bitrate,
                fps,
                codec,
                audioCodec,
                user_id: req.session.userId
              };

              const video = await Video.create(videoData);

              // Add video to processing queue for streaming-ready conversion
              try {
                await videoProcessingService.addToQueue(video.id);
                console.log(`[ChunkedUpload] Video ${video.id} added to processing queue`);
              } catch (processingError) {
                console.error('[ChunkedUpload] Error adding video to processing queue:', processingError);
                // Don't fail the upload, just log the error
              }

              // Update storage usage
              const fileSizeGB = fileSize / (1024 * 1024 * 1024);
              await Subscription.updateStorageUsage(req.session.userId, fileSizeGB);

              const uploadTime = Date.now() - startTime;
              console.log(`✅ Chunked upload completed successfully in ${(uploadTime / 1000).toFixed(2)}s`);
              console.log(`📊 Video saved: ${video.title} (ID: ${video.id})`);

              res.json({
                success: true,
                message: 'Chunked upload completed successfully',
                video
              });
              resolve();
            } catch (dbError) {
              console.error('Database error:', dbError);
              // Clean up files on error
              try {
                if (fs.existsSync(fullFilePath)) fs.unlinkSync(fullFilePath);
                if (fs.existsSync(fullThumbnailPath)) fs.unlinkSync(fullThumbnailPath);
              } catch (cleanupError) {
                console.error('Error cleaning up files:', cleanupError);
              }
              reject(dbError);
            }
          })
          .on('error', (err) => {
            console.error('Error creating thumbnail for chunked upload:', err);
            // Continue without thumbnail
            const videoDataFallback = {
              title,
              filepath: filePath,
              thumbnail_path: null,
              file_size: fileSize,
              duration,
              format,
              resolution,
              bitrate,
              fps,
              codec,
              audioCodec,
              user_id: req.session.userId
            };

            Video.create(videoDataFallback)
              .then(async (video) => {
                const fileSizeGB = fileSize / (1024 * 1024 * 1024);
                await Subscription.updateStorageUsage(req.session.userId, fileSizeGB);

                const uploadTime = Date.now() - startTime;
                console.log(`✅ Chunked upload completed successfully in ${(uploadTime / 1000).toFixed(2)}s (without thumbnail)`);

                res.json({
                  success: true,
                  message: 'Chunked upload completed successfully (thumbnail generation failed)',
                  video
                });
                resolve();
              })
              .catch((dbError) => {
                console.error('Database error in fallback:', dbError);
                try {
                  if (fs.existsSync(fullFilePath)) fs.unlinkSync(fullFilePath);
                } catch (cleanupError) {
                  console.error('Error cleaning up video file:', cleanupError);
                }
                reject(dbError);
              });
          })
          .run();
      });
    });

  } catch (error) {
    console.error('Error finalizing chunked upload:', error);
    res.status(500).json({
      error: 'Failed to finalize upload',
      details: error.message
    });
  }
});

// Get upload status
app.get('/api/videos/upload/status/:uploadId', isAuthenticated, async (req, res) => {
  try {
    const { uploadId } = req.params;
    const status = chunkedUploadService.getUploadStatus(uploadId);

    if (!status) {
      return res.status(404).json({
        error: 'Upload session not found or expired'
      });
    }

    res.json({
      success: true,
      status
    });

  } catch (error) {
    console.error('Error getting upload status:', error);
    res.status(500).json({
      error: 'Failed to get upload status',
      details: error.message
    });
  }
});

app.get('/api/videos', isAuthenticated, async (req, res) => {
  try {
    const videos = await Video.findAll(req.session.userId);
    res.json({ success: true, videos });
  } catch (error) {
    console.error('Error fetching videos:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch videos' });
  }
});
app.delete('/api/videos/:id', isAuthenticated, async (req, res) => {
  try {
    const videoId = req.params.id;
    const video = await Video.findById(videoId);
    if (!video) {
      return res.status(404).json({ success: false, error: 'Video not found' });
    }
    if (video.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }

    // Calculate storage to subtract (convert bytes to GB)
    const fileSizeGB = video.file_size ? video.file_size / (1024 * 1024 * 1024) : 0;
    console.log(`🗑️ Deleting video: ${video.title}, Size: ${fileSizeGB.toFixed(3)}GB`);

    // Delete video from database and files (consolidated in model)
    const deleteResult = await Video.delete(videoId, req.session.userId);

    if (!deleteResult.success) {
      return res.status(500).json({ success: false, error: deleteResult.error || 'Failed to delete video' });
    }

    // Update storage usage (subtract the file size)
    if (fileSizeGB > 0) {
      const isProduction = process.env.NODE_ENV === 'production';
      if (!isProduction) {
        console.debug(`📊 Updating storage: -${fileSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
      }
      await Subscription.updateStorageUsage(req.session.userId, -fileSizeGB);
      if (!isProduction) {
        // console.debug('✅ Storage updated successfully'); // Removed for production
      }
    }

    res.json({ success: true, message: 'Video deleted successfully' });
  } catch (error) {
    console.error('Error deleting video:', error);
    res.status(500).json({ success: false, error: 'Failed to delete video' });
  }
});
app.post('/api/videos/:id/rename', isAuthenticated, [
  body('title').trim().isLength({ min: 1 }).withMessage('Title cannot be empty')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ error: errors.array()[0].msg });
    }
    const video = await Video.findById(req.params.id);
    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }
    if (video.user_id !== req.session.userId) {
      return res.status(403).json({ error: 'You don\'t have permission to rename this video' });
    }
    await Video.update(req.params.id, { title: req.body.title });
    res.json({ success: true, message: 'Video renamed successfully' });
  } catch (error) {
    console.error('Error renaming video:', error);
    res.status(500).json({ error: 'Failed to rename video' });
  }
});

// Regenerate thumbnail for existing video
app.post('/api/videos/:id/regenerate-thumbnail', isAuthenticated, async (req, res) => {
  try {
    const video = await Video.findById(req.params.id);
    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }
    if (video.user_id !== req.session.userId) {
      return res.status(403).json({ error: 'You don\'t have permission to regenerate thumbnail for this video' });
    }

    const videoPath = path.join(__dirname, 'public', video.filepath);
    if (!fs.existsSync(videoPath)) {
      return res.status(404).json({ error: 'Video file not found' });
    }

    // console.log(`🔄 Regenerating thumbnail for video: ${video.title}`); // Removed for production
    // Generate new thumbnail filename
    const videoFilename = path.basename(video.filepath, path.extname(video.filepath));
    const thumbnailFilename = `thumb-${videoFilename}.jpg`;
    const thumbnailPath = `/uploads/thumbnails/${thumbnailFilename}`;
    const fullThumbnailPath = path.join(__dirname, 'public', thumbnailPath);

    // Ensure thumbnails directory exists
    const thumbnailDir = path.join(__dirname, 'public', 'uploads', 'thumbnails');
    if (!fs.existsSync(thumbnailDir)) {
      fs.mkdirSync(thumbnailDir, { recursive: true });
    }

    // Get video duration for seek time
    const metadata = await new Promise((resolve, reject) => {
      ffmpeg.ffprobe(videoPath, (err, metadata) => {
        if (err) return reject(err);
        resolve(metadata);
      });
    });

    const duration = metadata.format.duration || 10;
    const seekTime = duration > 10 ? '00:00:10' : Math.max(1, Math.floor(duration / 2));

    // console.log(`🖼️ Generating thumbnail: ${thumbnailFilename}, seek time: ${seekTime}`); // Removed for production
    // Generate thumbnail
    await new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .inputOptions(['-hwaccel', 'none', '-ss', seekTime.toString()])
        .outputOptions([
          '-vframes', '1',
          '-q:v', '2',
          '-vf', 'scale=854:480:force_original_aspect_ratio=decrease,pad=854:480:(ow-iw)/2:(oh-ih)/2'
        ])
        .output(fullThumbnailPath)
        .on('start', (commandLine) => {
          // console.log(`🎬 FFmpeg command: ${commandLine}`); // Removed for production
        })
        .on('end', async () => {
          // console.log(`✅ Thumbnail regenerated successfully: ${thumbnailFilename}`); // Removed for production
          // Update video record with new thumbnail path
          await Video.update(req.params.id, { thumbnail_path: thumbnailPath });

          resolve();
        })
        .on('error', (err) => {
          console.error('❌ Error regenerating thumbnail:', err);
          reject(err);
        })
        .run();
    });

    res.json({
      success: true,
      message: 'Thumbnail regenerated successfully',
      thumbnail_path: thumbnailPath
    });

  } catch (error) {
    console.error('Error regenerating thumbnail:', error);
    res.status(500).json({ error: 'Failed to regenerate thumbnail' });
  }
});

// Debug endpoint to verify video files exist
app.get('/api/videos/:id/verify-files', isAuthenticated, async (req, res) => {
  try {
    const video = await Video.findById(req.params.id);
    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }
    if (video.user_id !== req.session.userId) {
      return res.status(403).json({ error: 'You don\'t have permission to verify this video' });
    }

    const verification = await Video.verifyFiles(req.params.id);
    res.json({
      success: true,
      video: {
        id: video.id,
        title: video.title,
        user_id: video.user_id
      },
      verification
    });
  } catch (error) {
    console.error('Error verifying video files:', error);
    res.status(500).json({ error: 'Failed to verify video files' });
  }
});
app.get('/stream/:videoId', isAuthenticated, async (req, res) => {
  try {
    const videoId = req.params.videoId;
    const video = await Video.findById(videoId);
    if (!video) {
      return res.status(404).send('Video not found');
    }
    if (video.user_id !== req.session.userId) {
      return res.status(403).send('You do not have permission to access this video');
    }
    const videoPath = path.join(__dirname, 'public', video.filepath);

    // Check if file exists
    if (!fs.existsSync(videoPath)) {
      console.error(`Video file not found: ${videoPath}`);
      return res.status(404).send('Video file not found on server');
    }

    const stat = fs.statSync(videoPath);
    const fileSize = stat.size;
    const range = req.headers.range;

    // Set appropriate headers for video streaming
    res.setHeader('Content-Disposition', 'inline');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    res.setHeader('Accept-Ranges', 'bytes');

    // Determine content type based on file extension
    const ext = path.extname(video.filepath).toLowerCase();
    let contentType = 'video/mp4';
    if (ext === '.avi') contentType = 'video/x-msvideo';
    else if (ext === '.mov') contentType = 'video/quicktime';

    if (range) {
      const parts = range.replace(/bytes=/, '').split('-');
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunkSize = (end - start) + 1;

      if (start >= fileSize || end >= fileSize) {
        return res.status(416).send('Range Not Satisfiable');
      }

      const file = fs.createReadStream(videoPath, { start, end });
      res.writeHead(206, {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Content-Length': chunkSize,
        'Content-Type': contentType,
      });

      file.on('error', (err) => {
        console.error('File stream error:', err);
        if (!res.headersSent) {
          res.status(500).send('Error streaming video');
        }
      });

      file.pipe(res);
    } else {
      res.writeHead(200, {
        'Content-Length': fileSize,
        'Content-Type': contentType,
      });

      const stream = fs.createReadStream(videoPath);
      stream.on('error', (err) => {
        console.error('File stream error:', err);
        if (!res.headersSent) {
          res.status(500).send('Error streaming video');
        }
      });

      stream.pipe(res);
    }
  } catch (error) {
    console.error('Streaming error:', error);
    if (!res.headersSent) {
      res.status(500).send('Error streaming video');
    }
  }
});
app.get('/api/settings/gdrive-status', isAuthenticated, async (req, res) => {
  try {
    // console.log('[GDrive Status] Checking API key for user:', req.session.userId); // Removed for production
    const user = await User.findById(req.session.userId);
    // console.log('[GDrive Status] User found:', !!user, 'Has API key:', !!user?.gdrive_api_key); // Removed for production
    res.json({
      hasApiKey: !!user?.gdrive_api_key,
      message: user?.gdrive_api_key ? 'Google Drive API key is configured' : 'No Google Drive API key found',
      userId: req.session.userId,
      userExists: !!user
    });
  } catch (error) {
    console.error('Error checking Google Drive API status:', error);
    res.status(500).json({ error: 'Failed to check API key status' });
  }
});
app.post('/api/settings/gdrive-api-key', isAuthenticated, [
  body('apiKey').notEmpty().withMessage('API Key is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: errors.array()[0].msg
      });
    }
    await User.update(req.session.userId, {
      gdrive_api_key: req.body.apiKey
    });
    return res.json({
      success: true,
      message: 'Google Drive API key saved successfully!'
    });
  } catch (error) {
    console.error('Error saving Google Drive API key:', error);
    res.status(500).json({
      success: false,
      error: 'An error occurred while saving your Google Drive API key'
    });
  }
});
app.post('/api/videos/import-drive', isAuthenticated, [
  body('driveUrl').notEmpty().withMessage('Google Drive URL is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: errors.array()[0].msg });
    }
    const { driveUrl } = req.body;

    // Debug logging for troubleshooting
    // console.log('[Google Drive Import] Request received:', {
    //   userId: req.session.userId,
    //   driveUrl: driveUrl ? 'provided' : 'missing'
    // }); // Removed for production
    const user = await User.findById(req.session.userId);
    if (!user) {
      console.error('[Google Drive Import] User not found:', req.session.userId);
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    if (!user.gdrive_api_key) {
      // console.log('[Google Drive Import] No API key configured for user:', req.session.userId); // Removed for production
      return res.status(400).json({
        success: false,
        error: 'Google Drive API key is not configured'
      });
    }
    const { extractFileId, downloadFile } = require('./utils/googleDriveService');
    try {
      const fileId = extractFileId(driveUrl);
      const jobId = uuidv4();
      processGoogleDriveImport(jobId, user.gdrive_api_key, fileId, req.session.userId)
        .catch(err => console.error('Drive import failed:', err));
      return res.json({
        success: true,
        message: 'Video import started',
        jobId: jobId
      });
    } catch (error) {
      console.error('Google Drive URL parsing error:', error);
      return res.status(400).json({
        success: false,
        error: 'Invalid Google Drive URL format'
      });
    }
  } catch (error) {
    console.error('Error importing from Google Drive:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      userId: req.session?.userId,
      sessionExists: !!req.session
    });
    res.status(500).json({ success: false, error: 'Failed to import video' });
  }
});
app.get('/api/videos/import-status/:jobId', isAuthenticated, async (req, res) => {
  const jobId = req.params.jobId;
  if (!importJobs[jobId]) {
    return res.status(404).json({ success: false, error: 'Import job not found' });
  }
  return res.json({
    success: true,
    status: importJobs[jobId]
  });
});
const importJobs = {};
async function processGoogleDriveImport(jobId, apiKey, fileId, userId) {
  const { downloadFile } = require('./utils/googleDriveService');
  const { getVideoInfo, generateThumbnail } = require('./utils/videoProcessor');
  const fs = require('fs');

  importJobs[jobId] = {
    status: 'checking',
    progress: 0,
    message: 'Checking file and storage quota...'
  };

  try {
    // console.log(`[Google Drive Import] Starting import job ${jobId} for user ${userId}`); // Removed for production
    // console.log(`[Google Drive Import] File ID: ${fileId}`); // Removed for production

    // First, get file metadata to check size and storage quota
    const { google } = require('googleapis');
    const drive = google.drive({
      version: 'v3',
      auth: apiKey
    });

    const fileMetadata = await drive.files.get({
      fileId: fileId,
      fields: 'name,mimeType,size'
    });

    const fileSize = parseInt(fileMetadata.data.size) || 0;
    const fileSizeGB = fileSize / (1024 * 1024 * 1024);

    // Check storage quota before downloading
    const Permission = require('./models/Permission');
    const isAdmin = await Permission.isAdmin(userId);

    if (!isAdmin) {
      const Subscription = require('./models/Subscription');
      const quotaCheck = await Subscription.checkStorageLimit(userId, fileSizeGB);

      if (quotaCheck.hasLimit) {
        const availableGB = quotaCheck.availableStorage || 0;
        const maxGB = quotaCheck.maxStorage || 0;
        const currentGB = quotaCheck.currentStorage || 0;
        throw new Error(`Storage quota exceeded. File size: ${fileSizeGB.toFixed(2)}GB, Available: ${availableGB.toFixed(2)}GB (${currentGB.toFixed(2)}GB used of ${maxGB.toFixed(2)}GB). Please upgrade your plan or free up space.`);
      }
    }

    importJobs[jobId] = {
      status: 'downloading',
      progress: 0,
      message: 'Starting download...'
    };

    const result = await downloadFile(apiKey, fileId, (progress) => {
      let message = `Downloading ${progress.filename}: ${progress.progress}%`;

      // Enhanced progress message for chunked downloads
      if (progress.chunk && progress.totalChunks) {
        message = `Downloading ${progress.filename}: chunk ${progress.chunk}/${progress.totalChunks} (${progress.progress}%)`;
      } else if (progress.status === 'finalizing') {
        message = `Finalizing ${progress.filename}...`;
      }

      importJobs[jobId] = {
        status: 'downloading',
        progress: progress.progress,
        message: message,
        chunk: progress.chunk || null,
        totalChunks: progress.totalChunks || null
      };
    });

    // console.log(`[Google Drive Import] Download completed successfully`); // Removed for production
    // console.log(`[Google Drive Import] Download result:`, {
    //   filename: result.filename,
    //   originalFilename: result.originalFilename,
    //   localFilePath: result.localFilePath,
    //   fileSize: result.fileSize,
    //   mimeType: result.mimeType
    // }); // Removed for production
    importJobs[jobId] = {
      status: 'processing',
      progress: 100,
      message: 'Processing video...'
    };

    // Verify file exists before processing
    // console.log(`[Google Drive Import] Verifying file exists: ${result.localFilePath}`); // Removed for production
    if (!fs.existsSync(result.localFilePath)) {
      console.error(`[Google Drive Import] File not found after download: ${result.localFilePath}`);
      throw new Error(`Downloaded file not found: ${result.localFilePath}`);
    }

    const stats = fs.statSync(result.localFilePath);
    // console.log(`[Google Drive Import] File verification successful:`); // Removed for production
    // console.log(`[Google Drive Import] - File exists: true`); // Removed for production
    // console.log(`[Google Drive Import] - File size on disk: ${stats.size} bytes`); // Removed for production
    // console.log(`[Google Drive Import] - Expected size: ${result.fileSize} bytes`); // Removed for production
    console.log(`[Google Drive Import] - File permissions: ${stats.mode.toString(8)}`);

    // console.log(`[Google Drive Import] Starting video processing...`); // Removed for production
    // Get video information (includes metadata from ffprobe)
    const videoInfo = await getVideoInfo(result.localFilePath);

    // Extract resolution and bitrate from videoInfo (no need for separate ffprobe call)
    let resolution = videoInfo.resolution || '';
    let bitrate = null;

    // If bitrate is available from videoInfo, use it; otherwise calculate from file size and duration
    if (videoInfo.bitrate) {
      bitrate = Math.round(videoInfo.bitrate / 1000); // Convert to kbps
    } else if (videoInfo.duration && result.fileSize) {
      // Estimate bitrate from file size and duration
      const fileSizeBits = result.fileSize * 8;
      const estimatedBitrate = Math.round(fileSizeBits / videoInfo.duration / 1000);
      bitrate = estimatedBitrate;
    }

    const thumbnailName = path.basename(result.filename, path.extname(result.filename)) + '.jpg';
    const thumbnailRelativePath = await generateThumbnail(result.localFilePath, thumbnailName)
      .then(() => `/uploads/thumbnails/${thumbnailName}`)
      .catch(() => null);

    let format = path.extname(result.filename).toLowerCase().replace('.', '');
    if (!format) format = 'mp4';

    const videoData = {
      title: path.basename(result.originalFilename, path.extname(result.originalFilename)),
      filepath: `/uploads/videos/${result.filename}`,
      thumbnail_path: thumbnailRelativePath,
      file_size: result.fileSize,
      duration: videoInfo.duration,
      format: format,
      resolution: resolution,
      bitrate: bitrate,
      user_id: userId
    };

    const video = await Video.create(videoData);

    // Add video to processing queue for streaming-ready conversion
    try {
      await videoProcessingService.addToQueue(video.id);
      console.log(`[GoogleDrive] Video ${video.id} added to processing queue`);
    } catch (processingError) {
      console.error('[GoogleDrive] Error adding video to processing queue:', processingError);
      // Don't fail the import, just log the error
    }

    // Update storage usage for Google Drive import
    const finalFileSizeGB = result.fileSize / (1024 * 1024 * 1024);
    const isProduction = process.env.NODE_ENV === 'production';
    if (!isProduction) {
      console.debug(`📊 Updating storage for Google Drive import: +${finalFileSizeGB.toFixed(3)}GB for user ${userId}`);
    }
    await Subscription.updateStorageUsage(userId, finalFileSizeGB);
    if (!isProduction) {
      // console.debug('✅ Storage updated successfully for Google Drive import'); // Removed for production
    }

    importJobs[jobId] = {
      status: 'complete',
      progress: 100,
      message: 'Video imported successfully',
      videoId: video.id
    };
    setTimeout(() => {
      delete importJobs[jobId];
    }, 5 * 60 * 1000);
  } catch (error) {
    console.error('Error processing Google Drive import:', error);

    // Clean up any partially downloaded files
    if (error.localFilePath && fs.existsSync(error.localFilePath)) {
      try {
        fs.unlinkSync(error.localFilePath);
        // console.log(`[Google Drive Import] Cleaned up failed download: ${error.localFilePath}`); // Removed for production
      } catch (cleanupError) {
        console.error(`[Google Drive Import] Failed to cleanup file: ${cleanupError.message}`);
      }
    }

    // Provide more specific error messages
    let errorMessage = 'Failed to import video';
    if (error.message) {
      if (error.message.includes('Storage quota exceeded')) {
        errorMessage = error.message; // Use the detailed storage quota message
      } else if (error.message.includes('No such file or directory')) {
        errorMessage = 'Video file processing failed - file not found after download';
      } else if (error.message.includes('Invalid data found')) {
        errorMessage = 'Video file appears to be corrupted or invalid format';
      } else if (error.message.includes('Permission denied')) {
        errorMessage = 'Permission error accessing video file';
      } else if (error.message.includes('Only MP4 and MOV')) {
        errorMessage = error.message; // Use the file format restriction message
      } else {
        errorMessage = error.message;
      }
    }

    importJobs[jobId] = {
      status: 'failed',
      progress: 0,
      message: errorMessage
    };
    setTimeout(() => {
      delete importJobs[jobId];
    }, 5 * 60 * 1000);
  }
}
app.get('/api/stream/videos', isAuthenticated, async (req, res) => {
  try {
    const videos = await Video.findAll(req.session.userId);

    // Separate videos by processing status
    const readyVideos = [];
    const notReadyVideos = [];

    videos.forEach(video => {
      const duration = video.duration ? Math.floor(video.duration) : 0;
      const minutes = Math.floor(duration / 60);
      const seconds = Math.floor(duration % 60);
      const formattedDuration = `${minutes}:${seconds.toString().padStart(2, '0')}`;

      const videoData = {
        id: video.id,
        name: video.title,
        thumbnail: video.thumbnail_path,
        resolution: video.resolution || '1280x720',
        duration: formattedDuration,
        url: `/stream/${video.id}`,
        processing_status: video.processing_status || 'completed',
        streaming_ready: video.processing_status === 'completed' || !video.processing_status
      };

      // Only include videos that are ready for streaming
      if (videoData.streaming_ready) {
        readyVideos.push(videoData);
      } else {
        notReadyVideos.push(videoData);
      }
    });

    // Return only ready videos for stream creation (backward compatibility)
    // Include metadata about not ready videos for UI information
    const response = readyVideos;
    response.metadata = {
      totalVideos: videos.length,
      readyCount: readyVideos.length,
      notReadyCount: notReadyVideos.length,
      notReadyVideos: notReadyVideos
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching videos for stream:', error);
    res.status(500).json({ error: 'Failed to load videos' });
  }
});
const Stream = require('./models/Stream');
const { title } = require('process');
app.get('/api/streams', isAuthenticated, async (req, res) => {
  try {
    const filter = req.query.filter;
    const streams = await Stream.findAll(req.session.userId, filter);
    res.json({ success: true, streams });
  } catch (error) {
    console.error('Error fetching streams:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch streams' });
  }
});
app.post('/api/streams', isAuthenticated, QuotaMiddleware.checkValidSubscription(), QuotaMiddleware.checkStreamingQuota(), [
  body('streamTitle').trim().isLength({ min: 1 }).withMessage('Title is required'),
  body('rtmpUrl').trim().isLength({ min: 1 }).withMessage('RTMP URL is required'),
  body('streamKey').trim().isLength({ min: 1 }).withMessage('Stream key is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: errors.array()[0].msg });
    }

    // Enhanced RTMP and Stream Key validation
    const StreamKeyValidator = require('./utils/streamKeyValidator');
    const validation = StreamKeyValidator.validateRtmpConfig(req.body.rtmpUrl, req.body.streamKey);

    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid RTMP configuration',
        details: {
          errors: validation.errors,
          warnings: validation.warnings,
          suggestions: validation.suggestions,
          platform: validation.platform
        }
      });
    }
    const isInUse = await Stream.isStreamKeyInUse(req.body.streamKey, req.session.userId);
    if (isInUse) {
      return res.status(400).json({
        success: false,
        error: 'This stream key is already in use. Please use a different key.'
      });
    }
    let platform = 'Custom';
    let platform_icon = 'ti-broadcast';
    if (req.body.rtmpUrl.includes('youtube.com')) {
      platform = 'YouTube';
      platform_icon = 'ti-brand-youtube';
    } else if (req.body.rtmpUrl.includes('facebook.com')) {
      platform = 'Facebook';
      platform_icon = 'ti-brand-facebook';
    } else if (req.body.rtmpUrl.includes('twitch.tv')) {
      platform = 'Twitch';
      platform_icon = 'ti-brand-twitch';
    } else if (req.body.rtmpUrl.includes('tiktok.com')) {
      platform = 'TikTok';
      platform_icon = 'ti-brand-tiktok';
    } else if (req.body.rtmpUrl.includes('instagram.com')) {
      platform = 'Instagram';
      platform_icon = 'ti-brand-instagram';
    } else if (req.body.rtmpUrl.includes('shopee.io')) {
      platform = 'Shopee Live';
      platform_icon = 'ti-shopping-bag';
    } else if (req.body.rtmpUrl.includes('restream.io')) {
      platform = 'Restream.io';
      platform_icon = 'ti-live-photo';
    }
    // Validate video if provided
    if (req.body.videoId) {
      const video = await Video.findById(req.body.videoId);
      if (!video || video.user_id !== req.session.userId) {
        return res.status(404).json({ success: false, error: 'Video not found' });
      }

      // Check if video is ready for streaming
      if (video.processing_status && video.processing_status !== 'completed') {
        const statusMessages = {
          'pending': 'Video is still waiting to be processed. Please wait for processing to complete before creating a stream.',
          'processing': 'Video is currently being processed. Please wait for processing to complete before creating a stream.',
          'failed': 'Video processing failed. Please try reprocessing the video or use a different video.'
        };

        return res.status(400).json({
          success: false,
          error: statusMessages[video.processing_status] || 'Video is not ready for streaming',
          processing_status: video.processing_status,
          video_title: video.title
        });
      }
    }

    const streamData = {
      title: req.body.streamTitle,
      video_id: req.body.videoId || null,
      rtmp_url: req.body.rtmpUrl,
      stream_key: req.body.streamKey,
      platform,
      platform_icon,
      bitrate: parseInt(req.body.bitrate) || 2500,
      resolution: req.body.resolution || '1280x720',
      fps: parseInt(req.body.fps) || 30,
      orientation: req.body.orientation || 'horizontal',
      loop_video: req.body.loopVideo === 'true' || req.body.loopVideo === true,
      use_advanced_settings: req.body.useAdvancedSettings === 'true' || req.body.useAdvancedSettings === true,
      user_id: req.session.userId
    };
    // Check if user can use advanced settings based on plan price (except for admin)
    if (streamData.use_advanced_settings) {
      const user = await User.findById(req.session.userId);

      // Admin users have unlimited access
      if (user && user.role === 'admin') {
        // console.log('👑 Admin user creating stream with advanced settings - access granted'); // Removed for production
      } else {
        // For non-admin users, check plan price
        const QuotaMiddleware = require('./middleware/quotaMiddleware');
        const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);

        if (quotaInfo.plan) {
          const planPrice = parseFloat(quotaInfo.plan.price) || 0;
          if (planPrice < 49900) {
            console.log(`🚫 Advanced settings blocked - Plan: ${quotaInfo.plan.name}, Price: Rp. ${planPrice.toLocaleString('id-ID')}`);

            // Get eligible plans dynamically
            const QuotaMiddleware = require('./middleware/quotaMiddleware');
            let errorMessage = 'Advanced settings hanya tersedia di plan premium. Silakan upgrade plan Anda untuk menggunakan fitur ini.';

            try {
              const eligiblePlans = await QuotaMiddleware.getAdvancedSettingsEligiblePlans();
              if (eligiblePlans.length > 0) {
                const planNames = eligiblePlans.map(plan => plan.name).join(', ');
                errorMessage = `Advanced settings hanya tersedia di plan ${planNames}. Silakan upgrade plan Anda untuk menggunakan fitur ini.`;
              }
            } catch (error) {
              // console.log('⚠️ Could not get eligible plans for error message, using default'); // Removed for production
            }

            return res.status(403).json({
              success: false,
              error: errorMessage,
              requiresUpgrade: true,
              currentPlanPrice: planPrice,
              minimumPrice: 49900
            });
          }
        } else {
          // console.log('🚫 Advanced settings blocked - No plan found'); // Removed for production
          // Get eligible plans dynamically
          const QuotaMiddleware = require('./middleware/quotaMiddleware');
          let errorMessage = 'Advanced settings hanya tersedia di plan premium. Silakan upgrade plan Anda.';

          try {
            const eligiblePlans = await QuotaMiddleware.getAdvancedSettingsEligiblePlans();
            if (eligiblePlans.length > 0) {
              const planNames = eligiblePlans.map(plan => plan.name).join(', ');
              errorMessage = `Advanced settings hanya tersedia di plan ${planNames}. Silakan upgrade plan Anda.`;
            }
          } catch (error) {
            // console.log('⚠️ Could not get eligible plans for error message, using default'); // Removed for production
          }

          return res.status(403).json({
            success: false,
            error: errorMessage,
            requiresUpgrade: true,
            currentPlanPrice: 0,
            minimumPrice: 50000
          });
        }
      }
    }

    if (req.body.scheduleTime) {
      const { datetimeLocalToUTC } = require('./utils/timezone');
      const timezone = req.body.scheduleTimezone || 'UTC';

      // Validate schedule time is not in the past
      const scheduleTimeUTC = datetimeLocalToUTC(req.body.scheduleTime, timezone);
      const now = new Date();
      const minimumTime = new Date(now.getTime() + (1 * 60 * 1000)); // 1 minute buffer

      // Debug logging for schedule validation
      console.log(`[Schedule Validation] Input: ${req.body.scheduleTime} (${timezone})`);
      // console.log(`[Schedule Validation] Converted UTC: ${scheduleTimeUTC}`); // Removed for production
      console.log(`[Schedule Validation] Current time: ${now.toISOString()}`);
      console.log(`[Schedule Validation] Minimum time: ${minimumTime.toISOString()}`);
      console.log(`[Schedule Validation] Time difference: ${(new Date(scheduleTimeUTC).getTime() - now.getTime()) / 1000} seconds`);

      if (new Date(scheduleTimeUTC) <= minimumTime) {
        // console.log(`[Schedule Validation] REJECTED: Schedule time is too close to current time`); // Removed for production
        return res.status(400).json({
          success: false,
          error: 'Schedule time must be at least 1 minute in the future'
        });
      }

      // console.log(`[Schedule Validation] ACCEPTED: Schedule time is valid`); // Removed for production
      streamData.schedule_time = scheduleTimeUTC;
      streamData.schedule_timezone = timezone;
    }
    if (req.body.duration) {
      streamData.duration = parseInt(req.body.duration);
    }
    streamData.status = req.body.scheduleTime ? 'scheduled' : 'offline';
    const stream = await Stream.create(streamData);
    res.json({ success: true, stream });
  } catch (error) {
    console.error('Error creating stream:', error);
    res.status(500).json({ success: false, error: 'Failed to create stream' });
  }
});

// API endpoint untuk mendapatkan status stream real-time (with caching)
// MUST be before /api/streams/:id to avoid route conflict
app.get('/api/streams/status', isAuthenticated, async (req, res) => {
  try {
    // Cache disabled - skip cache check

    const streams = await Stream.findAll(req.session.userId);
    const activeStreamIds = streamingService.getActiveStreams();

    // Update status berdasarkan active streams di memory with enhanced validation
    const updatedStreams = streams.map(stream => {
      const isInMemory = activeStreamIds.includes(stream.id);
      const isValidProcess = isInMemory && streamingService.validateStreamProcess(stream.id);

      // Check if stream is newly started (within last 10 seconds)
      let isNewlyStarted = false;
      if (isInMemory && stream.status === 'live' && stream.start_time) {
        const startTime = new Date(stream.start_time);
        const timeSinceStart = Date.now() - startTime.getTime();
        isNewlyStarted = timeSinceStart < 10000; // Less than 10 seconds
      }

      // Don't show inconsistent status for newly started streams
      let actualStatus = stream.status;
      if (stream.status === 'live') {
        if (isValidProcess) {
          actualStatus = 'live';
        } else if (isNewlyStarted) {
          actualStatus = 'live'; // Keep as live for newly started streams
        } else if (!isInMemory) {
          actualStatus = 'inconsistent';
        } else {
          actualStatus = 'live'; // Process exists but might be starting
        }
      }

      return {
        ...stream,
        isReallyActive: isValidProcess || isNewlyStarted,
        actualStatus: actualStatus,
        processValid: isInMemory ? isValidProcess : null,
        isNewlyStarted: isNewlyStarted
      };
    });

    const result = {
      success: true,
      streams: updatedStreams,
      activeCount: activeStreamIds.length,
      totalCount: streams.length
    };

    // Cache disabled - skip caching result

    res.json(result);
  } catch (error) {
    console.error('Error fetching stream status:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch stream status' });
  }
});

app.get('/api/streams/:id', isAuthenticated, async (req, res) => {
  try {
    const stream = await Stream.getStreamWithVideo(req.params.id);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized to access this stream' });
    }
    res.json({ success: true, stream });
  } catch (error) {
    console.error('Error fetching stream:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch stream' });
  }
});
app.put('/api/streams/:id', isAuthenticated, async (req, res) => {
  try {
    const stream = await Stream.findById(req.params.id);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized to update this stream' });
    }

    // 🔒 PENGAMANAN ENHANCED: Cek apakah stream sedang berjalan (baik status live maupun process aktif)
    const isStreamActive = streamingService.isStreamActive(req.params.id);
    const isStreamLive = stream.status === 'live';
    const isProcessValid = isStreamActive ? streamingService.validateStreamProcess(req.params.id) : false;

    // Stream dianggap "running" jika:
    // 1. Status live DAN process aktif, ATAU
    // 2. Process aktif dan valid (meskipun status belum terupdate)
    const isStreamRunning = (isStreamLive && isStreamActive) || (isStreamActive && isProcessValid);

    if (isStreamRunning) {
      console.log(`[API] 🔒 Edit blocked - Stream ${req.params.id} is currently running`);
      console.log(`  - DB Status: ${stream.status}`);
      console.log(`  - In Memory: ${isStreamActive}`);
      console.log(`  - Process Valid: ${isProcessValid}`);

      return res.status(423).json({
        success: false,
        error: 'Stream sedang berjalan dan tidak dapat diedit. Silakan stop stream terlebih dahulu.',
        code: 'STREAM_LIVE_LOCKED',
        streamStatus: stream.status,
        isActive: isStreamActive,
        isProcessValid: isProcessValid,
        isRunning: true
      });
    }
    const updateData = {};
    if (req.body.streamTitle) updateData.title = req.body.streamTitle;
    if (req.body.videoId) {
      // Validate video if being changed
      const video = await Video.findById(req.body.videoId);
      if (!video || video.user_id !== req.session.userId) {
        return res.status(404).json({ success: false, error: 'Video not found' });
      }

      // Check if video is ready for streaming
      if (video.processing_status && video.processing_status !== 'completed') {
        const statusMessages = {
          'pending': 'Video is still waiting to be processed. Please wait for processing to complete before using this video.',
          'processing': 'Video is currently being processed. Please wait for processing to complete before using this video.',
          'failed': 'Video processing failed. Please try reprocessing the video or use a different video.'
        };

        return res.status(400).json({
          success: false,
          error: statusMessages[video.processing_status] || 'Video is not ready for streaming',
          processing_status: video.processing_status,
          video_title: video.title
        });
      }

      updateData.video_id = req.body.videoId;
    }
    if (req.body.rtmpUrl) {
      updateData.rtmp_url = req.body.rtmpUrl;

      // Update platform and platform_icon based on new RTMP URL
      let platform = 'Custom';
      let platform_icon = 'ti-broadcast';
      if (req.body.rtmpUrl.includes('youtube.com')) {
        platform = 'YouTube';
        platform_icon = 'ti-brand-youtube';
      } else if (req.body.rtmpUrl.includes('facebook.com')) {
        platform = 'Facebook';
        platform_icon = 'ti-brand-facebook';
      } else if (req.body.rtmpUrl.includes('twitch.tv')) {
        platform = 'Twitch';
        platform_icon = 'ti-brand-twitch';
      } else if (req.body.rtmpUrl.includes('tiktok.com')) {
        platform = 'TikTok';
        platform_icon = 'ti-brand-tiktok';
      } else if (req.body.rtmpUrl.includes('instagram.com')) {
        platform = 'Instagram';
        platform_icon = 'ti-brand-instagram';
      } else if (req.body.rtmpUrl.includes('shopee.io')) {
        platform = 'Shopee Live';
        platform_icon = 'ti-shopping-bag';
      } else if (req.body.rtmpUrl.includes('restream.io')) {
        platform = 'Restream.io';
        platform_icon = 'ti-live-photo';
      }
      updateData.platform = platform;
      updateData.platform_icon = platform_icon;
    }
    if (req.body.streamKey) updateData.stream_key = req.body.streamKey;
    if (req.body.bitrate) updateData.bitrate = parseInt(req.body.bitrate);
    if (req.body.resolution) updateData.resolution = req.body.resolution;
    if (req.body.fps) updateData.fps = parseInt(req.body.fps);
    if (req.body.orientation) updateData.orientation = req.body.orientation;
    if (req.body.loopVideo !== undefined) {
      updateData.loop_video = req.body.loopVideo === 'true' || req.body.loopVideo === true;
    }
    if (req.body.useAdvancedSettings !== undefined) {
      updateData.use_advanced_settings = req.body.useAdvancedSettings === 'true' || req.body.useAdvancedSettings === true;

      // Check if user can use advanced settings based on plan price (except for admin)
      if (updateData.use_advanced_settings) {
        const user = await User.findById(req.session.userId);

        // Admin users have unlimited access
        if (user && user.role === 'admin') {
          // console.log('👑 Admin user updating stream with advanced settings - access granted'); // Removed for production
        } else {
          // For non-admin users, check plan price
          const QuotaMiddleware = require('./middleware/quotaMiddleware');
          const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);

          if (quotaInfo.plan) {
            const planPrice = parseFloat(quotaInfo.plan.price) || 0;
            if (planPrice < 49900) {
              console.log(`🚫 Advanced settings blocked - Plan: ${quotaInfo.plan.name}, Price: Rp. ${planPrice.toLocaleString('id-ID')}`);
              // Get eligible plans dynamically
              const QuotaMiddleware = require('./middleware/quotaMiddleware');
              let errorMessage = 'Advanced settings hanya tersedia di plan premium. Silakan upgrade plan Anda untuk menggunakan fitur ini.';

              try {
                const eligiblePlans = await QuotaMiddleware.getAdvancedSettingsEligiblePlans();
                if (eligiblePlans.length > 0) {
                  const planNames = eligiblePlans.map(plan => plan.name).join(', ');
                  errorMessage = `Advanced settings hanya tersedia di plan ${planNames}. Silakan upgrade plan Anda untuk menggunakan fitur ini.`;
                }
              } catch (error) {
                // console.log('⚠️ Could not get eligible plans for error message, using default'); // Removed for production
              }

              return res.status(403).json({
                success: false,
                error: errorMessage,
                requiresUpgrade: true,
                currentPlanPrice: planPrice,
                minimumPrice: 49900
              });
            }
          } else {
            // console.log('🚫 Advanced settings blocked - No plan found'); // Removed for production
            // Get eligible plans dynamically
            const QuotaMiddleware = require('./middleware/quotaMiddleware');
            let errorMessage = 'Advanced settings hanya tersedia di plan premium. Silakan upgrade plan Anda.';

            try {
              const eligiblePlans = await QuotaMiddleware.getAdvancedSettingsEligiblePlans();
              if (eligiblePlans.length > 0) {
                const planNames = eligiblePlans.map(plan => plan.name).join(', ');
                errorMessage = `Advanced settings hanya tersedia di plan ${planNames}. Silakan upgrade plan Anda.`;
              }
            } catch (error) {
              // console.log('⚠️ Could not get eligible plans for error message, using default'); // Removed for production
            }

            return res.status(403).json({
              success: false,
              error: errorMessage,
              requiresUpgrade: true,
              currentPlanPrice: 0,
              minimumPrice: 49900
            });
          }
        }
      }
    }
    if (req.body.scheduleTime) {
      const { datetimeLocalToUTC } = require('./utils/timezone');
      const timezone = req.body.scheduleTimezone || 'UTC';

      // Validate schedule time is not in the past
      const scheduleTimeUTC = datetimeLocalToUTC(req.body.scheduleTime, timezone);
      const now = new Date();
      const minimumTime = new Date(now.getTime() + (1 * 60 * 1000)); // 1 minute buffer

      // Debug logging for schedule validation (edit stream)
      console.log(`[Edit Schedule Validation] Input: ${req.body.scheduleTime} (${timezone})`);
      // console.log(`[Edit Schedule Validation] Converted UTC: ${scheduleTimeUTC}`); // Removed for production
      console.log(`[Edit Schedule Validation] Current time: ${now.toISOString()}`);
      console.log(`[Edit Schedule Validation] Minimum time: ${minimumTime.toISOString()}`);
      console.log(`[Edit Schedule Validation] Time difference: ${(new Date(scheduleTimeUTC).getTime() - now.getTime()) / 1000} seconds`);

      if (new Date(scheduleTimeUTC) <= minimumTime) {
        // console.log(`[Edit Schedule Validation] REJECTED: Schedule time is too close to current time`); // Removed for production
        return res.status(400).json({
          success: false,
          error: 'Schedule time must be at least 1 minute in the future'
        });
      }

      // console.log(`[Edit Schedule Validation] ACCEPTED: Schedule time is valid`); // Removed for production
      updateData.schedule_time = scheduleTimeUTC;
      updateData.schedule_timezone = timezone;
      updateData.status = 'scheduled';
    } else if ('scheduleTime' in req.body && !req.body.scheduleTime) {
      updateData.schedule_time = null;
      updateData.schedule_timezone = null;
      updateData.status = 'offline';
    }
    if (req.body.duration !== undefined) {
      const newDuration = parseInt(req.body.duration);
      updateData.duration = newDuration;

      // If duration is set to 0, cancel any scheduled termination
      if (newDuration === 0) {
        const schedulerService = require('./services/schedulerService');
        const cancelled = schedulerService.cancelStreamTermination(req.params.id);
        if (cancelled) {
          console.log(`[API] Cancelled scheduled termination for stream ${req.params.id} (duration set to 0)`);
        }
      }
    }
    // Check if stream is currently live and if critical settings are being changed
    const isCurrentlyLive = stream.status === 'live';
    const criticalSettings = ['bitrate', 'resolution', 'fps', 'video_id', 'rtmp_url', 'stream_key', 'orientation', 'loop_video'];
    const criticalSettingsChanged = criticalSettings.some(setting => updateData.hasOwnProperty(setting));

    // Log the analysis for debugging
    console.log(`[API] Stream ${req.params.id} edit analysis:`);
    console.log(`  - Current status: ${stream.status}`);
    console.log(`  - Is currently live: ${isCurrentlyLive}`);
    console.log(`  - Settings being updated: ${Object.keys(updateData).join(', ')}`);
    console.log(`  - Critical settings changed: ${criticalSettingsChanged}`);
    if (criticalSettingsChanged) {
      const changedCriticalSettings = criticalSettings.filter(setting => updateData.hasOwnProperty(setting));
      console.log(`  - Changed critical settings: ${changedCriticalSettings.join(', ')}`);
    }

    let streamRestarted = false;
    let restartError = null;

    // If stream is live and critical settings changed, restart the stream
    if (isCurrentlyLive && criticalSettingsChanged) {
      try {
        console.log(`[API] Stream ${req.params.id} is live and critical settings changed. Restarting stream...`);

        // First update the database with new settings
        await Stream.update(req.params.id, updateData);

        // Stop the current stream
        const stopResult = await streamingService.stopStream(req.params.id);
        if (!stopResult.success) {
          console.warn(`[API] Warning: Failed to stop stream ${req.params.id} cleanly:`, stopResult.error);
        }

        // Wait a moment for cleanup
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Restart the stream with new settings
        const startResult = await streamingService.startStream(req.params.id);
        if (startResult.success) {
          console.log(`[API] ✅ Successfully restarted stream ${req.params.id} with new settings`);
          streamRestarted = true;
        } else {
          console.error(`[API] ❌ Failed to restart stream ${req.params.id}:`, startResult.error);
          restartError = startResult.error;
          // Update status to offline since restart failed
          await Stream.updateStatus(req.params.id, 'offline', req.session.userId);
        }
      } catch (error) {
        console.error(`[API] ❌ Error during stream restart for ${req.params.id}:`, error);
        restartError = error.message;
        // Ensure stream is marked as offline if restart failed
        try {
          await Stream.updateStatus(req.params.id, 'offline', req.session.userId);
        } catch (statusError) {
          console.error(`[API] ❌ Error updating stream status to offline:`, statusError);
        }
      }
    } else {
      // Normal update - stream is not live or no critical settings changed
      await Stream.update(req.params.id, updateData);
    }

    // Get the updated stream data
    const updatedStream = await Stream.findById(req.params.id);

    // Clear failed stream status when RTMP settings are updated
    // This allows users to retry with new settings without manual clear
    let failedStatusCleared = false;
    if (updateData.rtmp_url || updateData.stream_key) {
      const wasBlacklisted = await streamingService.clearFailedStream(req.params.id);
      if (wasBlacklisted) {
        // console.log(`[API] Automatically cleared failed status for stream ${req.params.id} after RTMP update`); // Removed for production
        failedStatusCleared = true;
      }
    }

    res.json({
      success: true,
      stream: updatedStream,
      failedStatusCleared: failedStatusCleared,
      streamRestarted: streamRestarted,
      restartError: restartError
    });
  } catch (error) {
    console.error('Error updating stream:', error);
    res.status(500).json({ success: false, error: 'Failed to update stream' });
  }
});
app.delete('/api/streams/:id', isAuthenticated, async (req, res) => {
  try {
    const stream = await Stream.findById(req.params.id);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized to delete this stream' });
    }

    // 🔒 PENGAMANAN ENHANCED: Cek apakah stream sedang berjalan (baik status live maupun process aktif)
    const isStreamActive = streamingService.isStreamActive(req.params.id);
    const isStreamLive = stream.status === 'live';
    const isProcessValid = isStreamActive ? streamingService.validateStreamProcess(req.params.id) : false;

    // Stream dianggap "running" jika:
    // 1. Status live DAN process aktif, ATAU
    // 2. Process aktif dan valid (meskipun status belum terupdate)
    const isStreamRunning = (isStreamLive && isStreamActive) || (isStreamActive && isProcessValid);

    if (isStreamRunning) {
      console.log(`[API] 🔒 Delete blocked - Stream ${req.params.id} is currently running`);
      console.log(`  - DB Status: ${stream.status}`);
      console.log(`  - In Memory: ${isStreamActive}`);
      console.log(`  - Process Valid: ${isProcessValid}`);

      return res.status(423).json({
        success: false,
        error: 'Stream sedang berjalan dan tidak dapat dihapus. Silakan stop stream terlebih dahulu.',
        code: 'STREAM_LIVE_LOCKED',
        streamStatus: stream.status,
        isActive: isStreamActive,
        isProcessValid: isProcessValid,
        isRunning: true
      });
    }

    await Stream.delete(req.params.id, req.session.userId);
    res.json({ success: true, message: 'Stream deleted successfully' });
  } catch (error) {
    console.error('Error deleting stream:', error);
    res.status(500).json({ success: false, error: 'Failed to delete stream' });
  }
});
app.post('/api/streams/:id/status', isAuthenticated, [
  body('status').isIn(['live', 'offline', 'scheduled']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: errors.array()[0].msg });
    }
    const streamId = req.params.id;
    const stream = await Stream.findById(streamId);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }
    const newStatus = req.body.status;
    if (newStatus === 'live') {
      if (stream.status === 'live') {
        return res.json({
          success: false,
          error: 'Stream is already live',
          stream
        });
      }
      if (!stream.video_id) {
        return res.json({
          success: false,
          error: 'No video attached to this stream',
          stream
        });
      }
      const result = await streamingService.startStream(streamId);
      if (result.success) {
        const updatedStream = await Stream.getStreamWithVideo(streamId);
        return res.json({
          success: true,
          stream: updatedStream,
          isAdvancedMode: result.isAdvancedMode
        });
      } else {
        return res.status(500).json({
          success: false,
          error: result.error || 'Failed to start stream'
        });
      }
    } else if (newStatus === 'offline') {
      if (stream.status === 'live') {
        const result = await streamingService.stopStream(streamId);
        if (!result.success) {
          console.warn('Failed to stop FFmpeg process:', result.error);
        }
        await Stream.update(streamId, {
          schedule_time: null
        });
        const isProduction = process.env.NODE_ENV === 'production';
        if (!isProduction) {
          // console.debug(`Reset schedule_time for stopped stream ${streamId}`); // Removed for production
        }
      } else if (stream.status === 'scheduled') {
        await Stream.update(streamId, {
          schedule_time: null,
          status: 'offline'
        });
        if (!isProduction) {
          // console.debug(`Scheduled stream ${streamId} was cancelled`); // Removed for production
        }
      }
      const result = await Stream.updateStatus(streamId, 'offline', req.session.userId);
      if (!result.updated) {
        return res.status(404).json({
          success: false,
          error: 'Stream not found or not updated'
        });
      }
      return res.json({ success: true, stream: result });
    } else {
      const result = await Stream.updateStatus(streamId, newStatus, req.session.userId);
      if (!result.updated) {
        return res.status(404).json({
          success: false,
          error: 'Stream not found or not updated'
        });
      }
      return res.json({ success: true, stream: result });
    }
  } catch (error) {
    console.error('Error updating stream status:', error);
    res.status(500).json({ success: false, error: 'Failed to update stream status' });
  }
});
app.get('/api/streams/check-key', isAuthenticated, async (req, res) => {
  try {
    console.log('[API] Stream key check request:', {
      userId: req.session.userId,
      hasKey: !!req.query.key,
      excludeId: req.query.excludeId
    });

    const streamKey = req.query.key;
    const excludeId = req.query.excludeId || null;
    if (!streamKey) {
      return res.status(400).json({
        success: false,
        error: 'Stream key is required'
      });
    }
    const isInUse = await Stream.isStreamKeyInUse(streamKey, req.session.userId, excludeId);

    console.log('[API] Stream key check result:', {
      streamKey: streamKey ? '[HIDDEN]' : 'empty',
      isInUse,
      userId: req.session.userId
    });

    res.json({
      success: true,
      isInUse: isInUse,
      message: isInUse ? 'Stream key is already in use' : 'Stream key is available'
    });
  } catch (error) {
    console.error('Error checking stream key:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check stream key'
    });
  }
});
app.get('/api/streams/:id/logs', isAuthenticated, async (req, res) => {
  try {
    const streamId = req.params.id;
    const stream = await Stream.findById(streamId);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }
    const logs = streamingService.getStreamLogs(streamId);
    const isActive = streamingService.isStreamActive(streamId);
    res.json({
      success: true,
      logs,
      isActive,
      stream
    });
  } catch (error) {
    console.error('Error fetching stream logs:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch stream logs' });
  }
});

// API endpoint untuk validasi RTMP configuration real-time
app.post('/api/validate-rtmp', isAuthenticated, [
  body('rtmpUrl').trim().isLength({ min: 1 }).withMessage('RTMP URL is required'),
  body('streamKey').trim().isLength({ min: 1 }).withMessage('Stream key is required')
], async (req, res) => {
  try {
    // Debug logging untuk troubleshooting
    console.log('[RTMP Validation] Request received:', {
      rtmpUrl: req.body.rtmpUrl,
      streamKey: req.body.streamKey ? '[HIDDEN]' : 'empty',
      userAgent: req.get('User-Agent'),
      host: req.get('Host'),
      origin: req.get('Origin'),
      referer: req.get('Referer'),
      sessionId: req.sessionID,
      userId: req.session?.userId
    });

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('[RTMP Validation] Validation errors:', errors.array());
      return res.status(400).json({ success: false, error: errors.array()[0].msg });
    }

    const StreamKeyValidator = require('./utils/streamKeyValidator');
    const validation = StreamKeyValidator.validateRtmpConfig(req.body.rtmpUrl, req.body.streamKey);

    // console.log('[RTMP Validation] Validation result:', {
    //   isValid: validation.isValid,
    //   platform: validation.platform,
    //   errorCount: (validation.errors && validation.errors.length) || 0,
    //   warningCount: (validation.warnings && validation.warnings.length) || 0
    // }); // Removed for production
    res.json({
      success: true,
      validation: {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        suggestions: validation.suggestions,
        platform: validation.platform,
        platformHelp: StreamKeyValidator.getPlatformHelp(validation.platform)
      }
    });
  } catch (error) {
    console.error('[RTMP Validation] Error validating RTMP config:', error);
    res.status(500).json({ success: false, error: 'Failed to validate RTMP configuration' });
  }
});

// API endpoint untuk clear failed stream status
app.post('/api/streams/:id/clear-failed', isAuthenticated, async (req, res) => {
  try {
    const streamId = req.params.id;
    const stream = await Stream.findById(streamId);

    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }

    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }

    const wasBlacklisted = await streamingService.clearFailedStream(streamId);

    res.json({
      success: true,
      message: wasBlacklisted ? 'Stream failure status cleared and status reset to offline' : 'Stream was not in failed state',
      wasBlacklisted,
      statusReset: stream.status === 'error'
    });
  } catch (error) {
    console.error('Error clearing failed stream:', error);
    res.status(500).json({ success: false, error: 'Failed to clear stream status' });
  }
});

// API endpoint untuk fix stream status inconsistency
app.post('/api/streams/:id/fix-status', isAuthenticated, async (req, res) => {
  try {
    const streamId = req.params.id;
    const stream = await Stream.findById(streamId);

    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }

    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }

    // Check if stream is actually active in memory
    const isInMemory = streamingService.getActiveStreams().includes(streamId);
    const isValidProcess = streamingService.validateStreamProcess(streamId);

    let message = '';

    if (isInMemory && isValidProcess) {
      // Stream is actually running, update DB to live
      await Stream.updateStatus(streamId, 'live', stream.user_id);
      message = 'Stream status synchronized - stream is now marked as live';
    } else if (!isInMemory) {
      // Stream is not running, update DB to offline
      await Stream.updateStatus(streamId, 'offline', stream.user_id);
      message = 'Stream status synchronized - stream is now marked as offline';
    } else {
      // Stream is in memory but process is invalid, clean up
      streamingService.cleanupStreamData(streamId);
      await Stream.updateStatus(streamId, 'offline', stream.user_id);
      message = 'Stream status fixed - cleaned up invalid process and marked as offline';
    }

    res.json({
      success: true,
      message: message
    });
  } catch (error) {
    console.error('Error fixing stream status:', error);
    res.status(500).json({ success: false, error: 'Failed to fix stream status' });
  }
});

// API endpoint untuk sync stream statuses manually (admin only)
app.post('/api/admin/sync-stream-status', isAuthenticated, Permission.requireRole('admin'), async (req, res) => {
  try {
    // console.log('[API] Manual stream status sync requested by admin'); // Removed for production
    await streamingService.syncStreamStatuses();
    res.json({
      success: true,
      message: 'Stream statuses synchronized successfully'
    });
  } catch (error) {
    console.error('[API] Error during manual stream status sync:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync stream statuses'
    });
  }
});

// API endpoint untuk cleanup orphaned FFmpeg processes (admin only)
app.post('/api/admin/cleanup-ffmpeg', isAuthenticated, Permission.requireRole('admin'), async (req, res) => {
  try {
    // console.log('[API] Manual FFmpeg cleanup requested by admin'); // Removed for production
    await streamingService.forceCleanupOrphanedProcesses();
    res.json({
      success: true,
      message: 'FFmpeg process cleanup initiated successfully'
    });
  } catch (error) {
    console.error('[API] Error cleaning up FFmpeg processes:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup FFmpeg processes'
    });
  }
});

// API endpoint to check current user's plan status
app.get('/api/user/plan-status', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.session.userId);
    const subscription = await Subscription.getUserSubscription(req.session.userId);
    const quotaCheck = await Subscription.checkStreamingSlotLimit(req.session.userId);
    const trialInfo = await User.hasActiveTrial(req.session.userId);

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        plan_type: user.plan_type,
        max_streaming_slots: user.max_streaming_slots,
        max_storage_gb: user.max_storage_gb
      },
      subscription: subscription,
      trial: trialInfo,
      quota: {
        currentSlots: quotaCheck.currentSlots,
        maxSlots: quotaCheck.maxSlots,
        hasLimit: quotaCheck.hasLimit
      }
    });
  } catch (error) {
    console.error('Error getting user plan status:', error);
    res.status(500).json({ success: false, error: 'Failed to get plan status' });
  }
});

// API endpoint to upgrade user plan (admin only) - TEMPORARY FOR TESTING
app.post('/api/admin/upgrade-user-plan', isAuthenticated, Permission.requireRole('admin'), async (req, res) => {
  try {
    const { userId, planName } = req.body;

    if (!userId || !planName) {
      return res.status(400).json({ success: false, error: 'User ID and plan name are required' });
    }

    // Get the plan details
    const plan = await Subscription.getPlanByName(planName);
    if (!plan) {
      return res.status(404).json({ success: false, error: 'Plan not found' });
    }

    // Update user plan
    await User.updatePlan(userId, planName, plan.max_streaming_slots, plan.max_storage_gb);

    console.log(`✅ User ${userId} upgraded to ${planName} plan`);
    res.json({
      success: true,
      message: `User upgraded to ${planName} plan successfully`,
      plan: {
        name: planName,
        max_streaming_slots: plan.max_streaming_slots,
        max_storage_gb: plan.max_storage_gb
      }
    });
  } catch (error) {
    console.error('Error upgrading user plan:', error);
    res.status(500).json({ success: false, error: 'Failed to upgrade user plan' });
  }
});

// API endpoint for debugging stream status issues (admin only)
app.get('/api/admin/debug/stream-status', isAuthenticated, Permission.requireRole('admin'), async (req, res) => {
  try {
    const activeStreamsInMemory = streamingService.getActiveStreams();
    const liveStreamsInDB = await Stream.findAll(null, 'live');
    const allStreams = await Stream.findAll();

    const debugInfo = {
      timestamp: new Date().toISOString(),
      activeStreamsInMemory: activeStreamsInMemory.length,
      liveStreamsInDB: liveStreamsInDB.length,
      totalStreams: allStreams.length,
      activeStreamIds: activeStreamsInMemory,
      liveStreamIds: liveStreamsInDB.map(s => s.id),
      inconsistencies: [],
      processValidation: {}
    };

    // Check for inconsistencies
    for (const stream of liveStreamsInDB) {
      const isInMemory = activeStreamsInMemory.includes(stream.id);
      const isValidProcess = isInMemory && streamingService.validateStreamProcess(stream.id);

      debugInfo.processValidation[stream.id] = {
        inMemory: isInMemory,
        validProcess: isValidProcess,
        dbStatus: stream.status,
        lastUpdated: stream.status_updated_at,
        title: stream.title,
        userId: stream.user_id
      };

      if (!isInMemory) {
        debugInfo.inconsistencies.push({
          streamId: stream.id,
          issue: 'marked_live_but_not_in_memory',
          dbStatus: stream.status,
          inMemory: false,
          title: stream.title
        });
      } else if (!isValidProcess) {
        debugInfo.inconsistencies.push({
          streamId: stream.id,
          issue: 'in_memory_but_invalid_process',
          dbStatus: stream.status,
          inMemory: true,
          validProcess: false,
          title: stream.title
        });
      }
    }

    // Check for streams in memory but not marked as live
    for (const streamId of activeStreamsInMemory) {
      const stream = liveStreamsInDB.find(s => s.id === streamId);
      if (!stream) {
        const dbStream = allStreams.find(s => s.id === streamId);
        debugInfo.inconsistencies.push({
          streamId: streamId,
          issue: 'in_memory_but_not_live_in_db',
          dbStatus: dbStream ? dbStream.status : 'not_found',
          inMemory: true,
          title: dbStream ? dbStream.title : 'Unknown'
        });
      }
    }

    // console.log(`[API] Debug info requested - Found ${debugInfo.inconsistencies.length} inconsistencies`); // Removed for production
    res.json({
      success: true,
      debug: debugInfo
    });
  } catch (error) {
    console.error('[API] Error getting debug info:', error);
    res.status(500).json({ success: false, error: 'Failed to get debug info' });
  }
});

app.get('/api/server-time', (req, res) => {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const month = monthNames[now.getMonth()];
  const year = now.getFullYear();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const formattedTime = `${day} ${month} ${year} ${hours}:${minutes}:${seconds}`;
  res.json({
    serverTime: now.toISOString(),
    formattedTime: formattedTime
  });
});

// API endpoint to get available timezones
app.get('/api/timezones', (req, res) => {
  try {
    const { getTimezoneList, getDefaultTimezone } = require('./utils/timezone');
    res.json({
      success: true,
      timezones: getTimezoneList(),
      defaultTimezone: getDefaultTimezone()
    });
  } catch (error) {
    console.error('Error getting timezones:', error);
    res.status(500).json({ success: false, error: 'Failed to get timezones' });
  }
});

// API endpoint to get copy-mode compatible settings for a video
app.get('/api/videos/:id/copy-mode-settings', isAuthenticated, async (req, res) => {
  try {
    const videoId = req.params.id;
    const Video = require('./models/Video');

    const video = await Video.findById(videoId);
    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    // Check if user owns this video
    if (video.user_id !== req.session.userId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    const streamingService = require('./services/streamingService');
    const compatibleSettings = streamingService.getCopyModeCompatibleSettings(video);

    if (!compatibleSettings) {
      return res.json({
        success: true,
        compatible: false,
        reason: 'Video codec or format not compatible with copy mode',
        videoProperties: {
          resolution: video.resolution,
          bitrate: video.bitrate,
          fps: video.fps,
          codec: video.codec,
          format: video.format
        }
      });
    }

    res.json({
      success: true,
      compatible: true,
      settings: compatibleSettings
    });

  } catch (error) {
    console.error('Error getting copy-mode settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get copy-mode settings'
    });
  }
});

// API endpoint to get current time in specific timezone
app.get('/api/time/:timezone', (req, res) => {
  try {
    const { getCurrentTimeInTimezone, formatDateTimeWithTimezone, isValidTimezone } = require('./utils/timezone');
    const timezone = req.params.timezone;

    if (!isValidTimezone(timezone)) {
      return res.status(400).json({ success: false, error: 'Invalid timezone' });
    }

    const currentTime = getCurrentTimeInTimezone(timezone);
    const formattedTime = formatDateTimeWithTimezone(currentTime, timezone);

    res.json({
      success: true,
      timezone: timezone,
      currentTime: currentTime.toISOString(),
      formattedTime: formattedTime
    });
  } catch (error) {
    console.error('Error getting time for timezone:', error);
    res.status(500).json({ success: false, error: 'Failed to get time for timezone' });
  }
});

// Video Processing API endpoints
app.get('/api/video-processing/status', isAuthenticated, async (req, res) => {
  try {
    const queueStatus = videoProcessingService.getQueueStatus();
    const processingStats = await videoProcessingService.getProcessingStats();

    res.json({
      success: true,
      queue: queueStatus,
      stats: processingStats
    });
  } catch (error) {
    console.error('Error getting video processing status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get processing status'
    });
  }
});

// Get video processing progress
app.get('/api/video-processing/progress/:videoId', isAuthenticated, async (req, res) => {
  try {
    const { videoId } = req.params;
    const progress = videoProcessingService.getProgress(videoId);

    if (!progress) {
      return res.json({
        success: true,
        progress: null,
        message: 'No processing progress found for this video'
      });
    }

    res.json({
      success: true,
      progress: progress
    });
  } catch (error) {
    console.error('Error getting video processing progress:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get processing progress'
    });
  }
});

// Get all video processing progress
app.get('/api/video-processing/progress', isAuthenticated, async (req, res) => {
  try {
    const allProgress = videoProcessingService.getAllProgress();

    res.json({
      success: true,
      progress: allProgress
    });
  } catch (error) {
    console.error('Error getting all video processing progress:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get processing progress'
    });
  }
});

app.post('/api/video-processing/reprocess/:videoId', isAuthenticated, async (req, res) => {
  try {
    const { videoId } = req.params;
    const video = await Video.findById(videoId);

    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    // Check if user owns the video (unless admin)
    const isAdmin = await Permission.isAdmin(req.session.userId);
    if (!isAdmin && video.user_id !== req.session.userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to reprocess this video'
      });
    }

    // Reset processing status and add to queue
    await Video.updateProcessingStatus(videoId, 'pending');
    const result = await videoProcessingService.addToQueue(videoId);

    res.json({
      success: true,
      message: 'Video added to processing queue',
      result
    });
  } catch (error) {
    console.error('Error reprocessing video:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reprocess video'
    });
  }
});

// Admin-only endpoints for video processing management
app.get('/api/admin/video-processing/detailed-status', isAuthenticated, async (req, res) => {
  try {
    const isAdmin = await Permission.isAdmin(req.session.userId);
    if (!isAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const detailedInfo = videoProcessingService.getDetailedQueueInfo();
    const configuration = videoProcessingService.getConfiguration();

    res.json({
      success: true,
      detailedInfo,
      configuration
    });
  } catch (error) {
    console.error('Error getting detailed video processing status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get detailed processing status'
    });
  }
});

app.post('/api/admin/video-processing/config', isAuthenticated, async (req, res) => {
  try {
    const isAdmin = await Permission.isAdmin(req.session.userId);
    if (!isAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { maxGlobalConcurrentJobs, maxUserConcurrentJobs } = req.body;
    const results = {};

    if (maxGlobalConcurrentJobs !== undefined) {
      results.globalLimit = videoProcessingService.setGlobalConcurrentJobLimit(maxGlobalConcurrentJobs);
    }

    if (maxUserConcurrentJobs !== undefined) {
      results.userLimit = videoProcessingService.setUserConcurrentJobLimit(maxUserConcurrentJobs);
    }

    const newConfig = videoProcessingService.getConfiguration();

    res.json({
      success: true,
      message: 'Configuration updated',
      results,
      configuration: newConfig
    });
  } catch (error) {
    console.error('Error updating video processing configuration:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update configuration'
    });
  }
});

app.delete('/api/admin/video-processing/user-queue/:userId', isAuthenticated, async (req, res) => {
  try {
    const isAdmin = await Permission.isAdmin(req.session.userId);
    if (!isAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { userId } = req.params;
    const removedJobs = videoProcessingService.removeUserFromQueue(userId);

    res.json({
      success: true,
      message: `Removed ${removedJobs} jobs from user ${userId}'s queue`,
      removedJobs
    });
  } catch (error) {
    console.error('Error removing user from video processing queue:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to remove user from queue'
    });
  }
});

// Health check endpoint for webhook testing - MUST be before 404 handler
app.get('/health', (req, res) => {
  // Set headers to bypass Cloudflare caching and protection
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'X-Robots-Tag': 'noindex, nofollow'
  });

  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'StreamOnPod',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    cloudflare: {
      country: req.headers['cf-ipcountry'] || 'unknown',
      ray: req.headers['cf-ray'] || 'unknown'
    }
  });
});

// 404 Handler - Must be placed after all routes but before error handler
app.use('*', async (req, res) => {
  const isProduction = process.env.NODE_ENV === 'production';
  if (!isProduction) {
    console.warn(`404 - Page not found: ${req.method} ${req.originalUrl}`);
  }

  // Check if it's an API request
  if (req.xhr || req.headers.accept?.indexOf('json') > -1 || req.originalUrl.startsWith('/api/')) {
    return res.status(404).json({
      success: false,
      error: 'Endpoint not found',
      message: `Cannot ${req.method} ${req.originalUrl}`
    });
  }

  // For web requests, render 404 page
  let user = null;
  if (req.session && req.session.userId) {
    try {
      const User = require('./models/User');
      user = await User.findById(req.session.userId);
    } catch (error) {
      console.error('Error fetching user for 404 page:', error);
    }
  }

  res.status(404).render('404', {
    title: 'Page Not Found',
    user: user,
    requestedUrl: req.originalUrl
  });
});

// Initialize database and services before starting server
async function initializeApplication() {
  try {
    // console.log('Checking database schema...'); // Removed for production
    // Ensure database schema exists before any services start
    await new Promise((resolve, reject) => {
      db.serialize(() => {
        // Check if tables exist, create if they don't
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='streams'", (err, row) => {
          if (err) {
            console.error('Error checking database schema:', err);
            return reject(err);
          }

          if (!row) {
            // console.log('⚠️  Database tables missing, running migration...'); // Removed for production
            // Run database migration
            require('./db/migrate')().then(() => {
              // console.log('✅ Database migration completed'); // Removed for production
              resolve();
            }).catch(reject);
          } else {
            // console.log('✅ Database schema verified'); // Removed for production
            resolve();
          }
        });
      });
    });

    const isProduction = process.env.NODE_ENV === 'production';
    if (!isProduction) {
      // console.info('Database migration completed successfully'); // Removed for production
    }
  } catch (error) {
    console.error('❌ Error during database initialization:', error);
    throw error;
  }
}

server.listen(port, '0.0.0.0', async () => {
  const ipAddresses = getLocalIpAddresses();
  const isProduction = process.env.NODE_ENV === 'production';

  console.log(`🚀 StreamOnPod server started on port ${port}`);
  console.log(`🌐 Access at: http://localhost:${port}`);
  console.log(`📦 Chunked upload enabled for files > 50MB`);
  console.log(`🔧 CSRF protection updated for API endpoints`);

  if (!isProduction) {
    if (ipAddresses && ipAddresses.length > 0) {
      ipAddresses.forEach(ip => {
        console.log(`🌐 Network access: http://${ip}:${port}`);
      });
    }
  }

  try {
    // Initialize database first
    await initializeApplication();

    // Initialize Socket.IO with notification service
    notificationService.setSocketIO(io);
    if (!isProduction) {
      // console.info('✅ Socket.IO and notification service initialized'); // Removed for production
    }

    // Create some initial notifications for testing (only in development)
    if (!isProduction) {
      try {
        // console.info('🔔 Creating initial test notifications...'); // Removed for production
        await notificationService.notifySuccess(
          'System Started',
          'StreamOnPod application has been started successfully',
          'normal'
        );
        await notificationService.notifySystemAlert(
          'High CPU Usage',
          'CPU usage has exceeded 80% for the last 5 minutes',
          'high'
        );
        await notificationService.notifyUserActivity(
          'New User Registration',
          'A new user has registered: testuser123',
          'normal'
        );
        // console.info('✅ Initial test notifications created'); // Removed for production
      } catch (error) {
        console.error('❌ Error creating initial notifications:', error);
      }
    }

    // Initialize streaming service with proper error handling
    if (!isProduction) {
      // console.info('[StreamingService] Cleaning up orphaned streams...'); // Removed for production
    }
    await streamingService.cleanupOrphanedStreams();

    // Initialize scheduler
    // console.log('[App] Initializing scheduler service...'); // Removed for production
    schedulerService.init(streamingService);
    // console.log('[App] Scheduler service initialized'); // Removed for production
    // Sync stream statuses
    if (!isProduction) {
      // console.info('[StreamingService] Syncing stream statuses...'); // Removed for production
    }
    await streamingService.syncStreamStatuses();

    // Initialize performance optimizations
    if (!isProduction) {
      // console.info('🚀 Initializing performance optimizations...'); // Removed for production
    }

    // Initialize database optimizations
    await dbOptimizer.initialize();

    // Cache disabled - skip preloading
    // console.log('Cache disabled - skipping data preloading'); // Removed for production
    // Precompress static assets
    const publicDir = path.join(__dirname, 'public');
    await staticOptimization.precompressAssets(publicDir);

    if (!isProduction) {
      // console.info('✅ Performance optimizations initialized successfully'); // Removed for production
    }

    // Initialize load balancer
    const loadBalancer = require('./services/loadBalancer');
    loadBalancer.start();
    if (!isProduction) {
      // console.info('✅ Load balancer started successfully'); // Removed for production
    }

    // Create initial admin notification
    await notificationService.notifySuccess(
      'System Started',
      'StreamOnPod application has been started successfully',
      'normal',
      { event: 'system_startup', timestamp: new Date().toISOString() }
    );

    // Initialize trial expiry checker
    if (!isProduction) {
      // console.info('🕒 Initializing trial expiry checker...'); // Removed for production
    }

    // Check for expired trials every hour
    setInterval(async () => {
      try {
        const expiredUsers = await Subscription.checkExpiredTrials();
        if (expiredUsers.length > 0) {
          if (!isProduction) {
            console.log(`⏰ Removed expired trials for ${expiredUsers.length} users`);
          }

          // Notify admin about expired trials
          await notificationService.notifySystemAlert(
            'Trial Expiry',
            `${expiredUsers.length} user trial(s) have expired and been removed`,
            'normal',
            {
              event: 'trial_expiry',
              expired_users: expiredUsers.map(u => u.username),
              timestamp: new Date().toISOString()
            }
          );
        }
      } catch (error) {
        console.error('❌ Error checking expired trials:', error);
      }
    }, 60 * 60 * 1000); // Check every hour

    // Run initial trial expiry check
    try {
      const expiredUsers = await Subscription.checkExpiredTrials();
      if (expiredUsers.length > 0) {
        if (!isProduction) {
          console.log(`⏰ Initial check: Removed expired trials for ${expiredUsers.length} users`);
        }
      }
    } catch (error) {
      console.error('❌ Error in initial trial expiry check:', error);
    }

    if (!isProduction) {
      // console.info('✅ Trial expiry checker initialized successfully'); // Removed for production
    }

    // Initialize transaction cleanup
    if (!isProduction) {
      // console.info('🧹 Initializing transaction cleanup...'); // Removed for production
    }

    // Cleanup expired pending transactions every 5 minutes
    setInterval(async () => {
      try {
        const Transaction = require('./models/Transaction');
        await Transaction.cleanupExpiredTransactions();
      } catch (error) {
        console.error('❌ Error during transaction cleanup:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Run initial transaction cleanup
    try {
      const Transaction = require('./models/Transaction');
      await Transaction.cleanupExpiredTransactions();
    } catch (error) {
      console.error('❌ Error in initial transaction cleanup:', error);
    }

    if (!isProduction) {
      // console.info('✅ Transaction cleanup initialized successfully'); // Removed for production
    }

    // Initialize subscription monitor
    try {
      console.log('🚀 Starting subscription monitor...');
      subscriptionMonitor.start();
      console.log('✅ Subscription monitor started successfully');
    } catch (error) {
      console.error('❌ Error starting subscription monitor:', error);
    }

  } catch (error) {
    console.error('❌ Error initializing services:', error);
    // Don't exit process, but log the error for monitoring
  }
});