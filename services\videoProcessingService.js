const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const Video = require('../models/Video');
const { getDetailedVideoInfo } = require('../utils/videoProcessor');

class VideoProcessingService {
  constructor() {
    // Global processing limits
    this.maxGlobalConcurrentJobs = 3; // Global limit to protect server
    this.maxUserConcurrentJobs = 1;   // Per-user limit for fairness
    this.activeJobs = 0;
    this.progressStore = new Map(); // Store progress for each video

    // User-based queue organization for fair processing
    this.userQueues = new Map();      // Map<userId, Array<jobData>>
    this.activeJobsByUser = new Map(); // Map<userId, number>
    this.lastProcessedUser = null;    // For round-robin scheduling

    // Legacy support (deprecated)
    this.processingQueue = new Map();
    this.isProcessing = false;
    this.maxConcurrentJobs = this.maxGlobalConcurrentJobs; // Backward compatibility

    // Start processing queue
    this.startQueueProcessor();
  }

  /**
   * Add video to processing queue
   */
  async addToQueue(videoId) {
    try {
      const video = await Video.findById(videoId);
      if (!video) {
        throw new Error('Video not found');
      }

      if (!video.user_id) {
        throw new Error('Video has no associated user');
      }

      // Check if already in queue or processed
      if (video.processing_status === 'completed' || video.processing_status === 'processing') {
        console.log(`[VideoProcessing] Video ${videoId} already processed or in progress`);
        return { success: true, message: 'Video already processed or in progress' };
      }

      // Check if video is already in any user queue
      if (this.isVideoInQueue(videoId)) {
        console.log(`[VideoProcessing] Video ${videoId} already in processing queue`);
        return { success: true, message: 'Video already in processing queue' };
      }

      const jobData = {
        id: videoId,
        userId: video.user_id,
        addedAt: new Date(),
        retries: 0
      };

      // Add to user-specific queue
      if (!this.userQueues.has(video.user_id)) {
        this.userQueues.set(video.user_id, []);
      }
      this.userQueues.get(video.user_id).push(jobData);

      // Legacy support - also add to old queue
      this.processingQueue.set(videoId, jobData);

      // Update status to pending
      await Video.updateProcessingStatus(videoId, 'pending');

      console.log(`[VideoProcessing] Added video ${videoId} from user ${video.user_id} to processing queue`);
      return { success: true, message: 'Video added to processing queue' };
    } catch (error) {
      console.error(`[VideoProcessing] Error adding video to queue:`, error);
      throw error;
    }
  }

  /**
   * Check if video is already in any user queue
   */
  isVideoInQueue(videoId) {
    for (const [userId, queue] of this.userQueues.entries()) {
      if (queue.some(job => job.id === videoId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Start queue processor
   */
  startQueueProcessor() {
    setInterval(async () => {
      // Check global limit
      if (this.activeJobs >= this.maxGlobalConcurrentJobs) {
        return;
      }

      const nextJob = this.getNextJobRoundRobin();
      if (nextJob) {
        this.processVideo(nextJob.id);
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Get next job using round-robin scheduling for fairness
   */
  getNextJobRoundRobin() {
    const userIds = Array.from(this.userQueues.keys()).filter(userId =>
      this.userQueues.get(userId).length > 0
    );

    if (userIds.length === 0) {
      return null;
    }

    // Find starting point for round-robin
    let startIndex = 0;
    if (this.lastProcessedUser) {
      const lastIndex = userIds.indexOf(this.lastProcessedUser);
      if (lastIndex !== -1) {
        startIndex = (lastIndex + 1) % userIds.length;
      }
    }

    // Round-robin through users to find next eligible job
    for (let i = 0; i < userIds.length; i++) {
      const userIndex = (startIndex + i) % userIds.length;
      const userId = userIds[userIndex];
      const userQueue = this.userQueues.get(userId);

      // Check if user has reached their concurrent job limit
      const userActiveJobs = this.activeJobsByUser.get(userId) || 0;
      if (userActiveJobs >= this.maxUserConcurrentJobs) {
        continue;
      }

      // Get next job from this user's queue
      if (userQueue.length > 0) {
        const job = userQueue.shift(); // Remove from front of queue
        this.lastProcessedUser = userId;

        // Clean up empty user queues
        if (userQueue.length === 0) {
          this.userQueues.delete(userId);
        }

        return job;
      }
    }

    return null;
  }

  /**
   * Get next job from queue (legacy method for backward compatibility)
   */
  getNextJob() {
    return this.getNextJobRoundRobin();
  }

  /**
   * Process a single video
   */
  async processVideo(videoId) {
    this.activeJobs++;

    // Get video to determine user
    const video = await Video.findById(videoId);
    const userId = video ? video.user_id : null;

    // Track user's active jobs
    if (userId) {
      const userActiveJobs = this.activeJobsByUser.get(userId) || 0;
      this.activeJobsByUser.set(userId, userActiveJobs + 1);
    }

    // Legacy support - remove from old queue
    this.processingQueue.delete(videoId);

    try {
      console.log(`[VideoProcessing] Starting processing for video ${videoId} (user: ${userId})`);

      // Update status to processing
      await Video.updateProcessingStatus(videoId, 'processing');

      // Initialize progress tracking
      this.updateProgress(videoId, 0, 'processing', 'Starting video processing...');

      const video = await Video.findById(videoId);
      if (!video) {
        throw new Error('Video not found');
      }

      // Check if video needs processing
      const needsProcessing = this.needsProcessing(video);
      
      if (!needsProcessing) {
        console.log(`[VideoProcessing] Video ${videoId} is already streaming-ready, marking as completed`);

        // Update progress to completed immediately
        this.updateProgress(videoId, 100, 'completed', 'Video is already streaming-ready');

        await Video.updateProcessingStatus(videoId, 'completed', video.filepath);

        // Clean up progress after a short delay
        setTimeout(() => {
          this.progressStore.delete(videoId);
        }, 3000);

        return { success: true, message: 'Video already streaming-ready' };
      }

      // Process the video
      const result = await this.convertToStreamingReady(video);
      
      if (result.success) {
        // Update video with processed information
        await Video.updateProcessingStatus(
          videoId, 
          'completed', 
          result.streamingReadyPath,
          result.metadata
        );
        
        console.log(`[VideoProcessing] Successfully processed video ${videoId}`);
      } else {
        throw new Error(result.error || 'Processing failed');
      }

    } catch (error) {
      console.error(`[VideoProcessing] Error processing video ${videoId}:`, error);

      // Update progress to failed
      this.updateProgress(videoId, 0, 'failed', `Processing failed: ${error.message}`);

      // Update status to failed
      await Video.updateProcessingStatus(videoId, 'failed');

      // Clean up progress after error (keep for longer to show error)
      setTimeout(() => {
        this.progressStore.delete(videoId);
      }, 10000);

      // Retry logic (max 2 retries)
      const job = this.processingQueue.get(videoId);
      if (job && job.retries < 2) {
        job.retries++;

        // Add back to user queue for retry
        if (userId) {
          if (!this.userQueues.has(userId)) {
            this.userQueues.set(userId, []);
          }
          this.userQueues.get(userId).unshift(job); // Add to front for priority
        }

        // Legacy support
        this.processingQueue.set(videoId, job);
        console.log(`[VideoProcessing] Retrying video ${videoId} (attempt ${job.retries + 1})`);

        // Reset progress for retry
        this.updateProgress(videoId, 0, 'processing', 'Retrying video processing...');
      }
    } finally {
      this.activeJobs--;

      // Decrease user's active job count
      if (userId) {
        const userActiveJobs = this.activeJobsByUser.get(userId) || 0;
        if (userActiveJobs > 0) {
          this.activeJobsByUser.set(userId, userActiveJobs - 1);
        }

        // Clean up if user has no active jobs
        if (this.activeJobsByUser.get(userId) === 0) {
          this.activeJobsByUser.delete(userId);
        }
      }
    }
  }

  /**
   * Check if video needs processing
   */
  needsProcessing(video) {
    // If already has streaming-ready path, no need to process
    if (video.streaming_ready_path) {
      return false;
    }

    // Check codec compatibility
    if (video.codec) {
      const codecLower = video.codec.toLowerCase();
      
      // HEVC/H.265, VP9, AV1 need re-encoding
      if (codecLower.includes('hevc') || codecLower.includes('h265') ||
          codecLower.includes('vp9') || codecLower.includes('av1')) {
        return true;
      }
    }

    // Check container format
    if (video.format && video.format.toLowerCase() === 'mkv') {
      return true;
    }

    // Check if video parameters are too high for streaming
    if (video.bitrate && video.bitrate > 5000) { // > 5Mbps
      return true;
    }

    if (video.resolution) {
      const [width, height] = video.resolution.split('x').map(Number);
      if (width > 1920 || height > 1080) { // > 1080p
        return true;
      }
    }

    // If H.264 and reasonable parameters, no processing needed
    if (video.codec && video.codec.toLowerCase().includes('h264')) {
      return false;
    }

    // Default: process if codec is unknown or not H.264
    return true;
  }

  /**
   * Convert video to streaming-ready format
   */
  async convertToStreamingReady(video) {
    return new Promise((resolve, reject) => {
      try {
        const inputPath = path.join(process.cwd(), 'public', video.filepath);
        const outputFilename = `processed_${uuidv4()}.mp4`;
        const outputPath = path.join(process.cwd(), 'public', 'uploads', 'videos', outputFilename);
        const outputRelativePath = `/uploads/videos/${outputFilename}`;

        // Ensure input file exists
        if (!fs.existsSync(inputPath)) {
          return reject(new Error('Input video file not found'));
        }

        console.log(`[VideoProcessing] Converting ${video.filepath} to streaming-ready format`);

        // Get original video bitrate to preserve quality
        const originalBitrate = video.bitrate || 4000; // Default to 4Mbps if unknown
        const targetBitrate = Math.min(Math.max(originalBitrate, 2000), 8000); // Clamp between 2-8 Mbps

        console.log(`[VideoProcessing] Original bitrate: ${originalBitrate}kbps, Target bitrate: ${targetBitrate}kbps`);

        // FFmpeg conversion to H.264 with quality-preserving settings
        ffmpeg(inputPath)
          .inputOptions([
            '-hwaccel', 'auto'
          ])
          .outputOptions([
            '-c:v', 'libx264',           // H.264 codec
            '-preset', 'medium',         // Balanced speed/quality
            '-crf', '20',                // Higher quality (lower CRF)
            '-maxrate', `${Math.round(targetBitrate * 1.2)}k`, // Allow 20% headroom
            '-bufsize', `${Math.round(targetBitrate * 2)}k`,   // Buffer size
            '-pix_fmt', 'yuv420p',       // Compatible pixel format
            '-profile:v', 'high',        // H.264 high profile
            '-level', '4.0',             // H.264 level 4.0
            '-g', '60',                  // GOP size
            '-keyint_min', '60',         // Min keyframe interval
            '-sc_threshold', '0',        // Scene change threshold
            '-c:a', 'aac',               // AAC audio codec
            '-b:a', '128k',              // Audio bitrate
            '-ar', '44100',              // Audio sample rate
            '-ac', '2',                  // Stereo audio
            '-movflags', '+faststart'    // Web-optimized MP4
          ])
          .output(outputPath)
          .on('start', (commandLine) => {
            console.log(`[VideoProcessing] FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            if (progress.percent) {
              const progressPercent = Math.round(progress.percent);
              console.log(`[VideoProcessing] Processing ${video.id}: ${progressPercent}%`);

              // Store progress for API access
              this.updateProgress(video.id, progressPercent, 'processing', `Processing video: ${progressPercent}%`);
            }
          })
          .on('end', async () => {
            try {
              // Update progress to 100% before completion
              this.updateProgress(video.id, 100, 'completed', 'Processing completed successfully');

              // Get metadata of processed video
              const processedMetadata = await getDetailedVideoInfo(outputPath);

              console.log(`[VideoProcessing] Successfully converted ${video.filepath} to ${outputRelativePath}`);

              // Clean up progress store after completion
              setTimeout(() => {
                this.progressStore.delete(video.id);
              }, 5000); // Keep progress for 5 seconds after completion

              resolve({
                success: true,
                streamingReadyPath: outputRelativePath,
                metadata: {
                  codec: 'h264',
                  resolution: processedMetadata.resolution || video.resolution,
                  bitrate: processedMetadata.bitrate || targetBitrate,
                  fps: processedMetadata.fps || video.fps
                }
              });
            } catch (metadataError) {
              console.error('[VideoProcessing] Error getting processed video metadata:', metadataError);
              resolve({
                success: true,
                streamingReadyPath: outputRelativePath,
                metadata: {
                  codec: 'h264',
                  resolution: video.resolution,
                  bitrate: targetBitrate,
                  fps: video.fps || 30
                }
              });
            }
          })
          .on('error', (error) => {
            console.error(`[VideoProcessing] FFmpeg error for ${video.id}:`, error);

            // Update progress to failed
            this.updateProgress(video.id, 0, 'failed', `Processing failed: ${error.message}`);

            // Clean up failed output file
            if (fs.existsSync(outputPath)) {
              try {
                fs.unlinkSync(outputPath);
              } catch (cleanupError) {
                console.error('[VideoProcessing] Error cleaning up failed file:', cleanupError);
              }
            }

            // Clean up progress store after error
            setTimeout(() => {
              this.progressStore.delete(video.id);
            }, 10000); // Keep error message for 10 seconds

            reject(new Error(`Video processing failed: ${error.message}`));
          })
          .run();

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get processing queue status
   */
  getQueueStatus() {
    // Calculate total queue length across all users
    let totalQueueLength = 0;
    const userQueueInfo = {};

    for (const [userId, queue] of this.userQueues.entries()) {
      totalQueueLength += queue.length;
      userQueueInfo[userId] = {
        queueLength: queue.length,
        activeJobs: this.activeJobsByUser.get(userId) || 0
      };
    }

    return {
      // New detailed information
      totalQueueLength,
      activeJobs: this.activeJobs,
      maxGlobalConcurrentJobs: this.maxGlobalConcurrentJobs,
      maxUserConcurrentJobs: this.maxUserConcurrentJobs,
      userQueues: userQueueInfo,
      lastProcessedUser: this.lastProcessedUser,

      // Legacy compatibility
      queueLength: this.processingQueue.size,
      maxConcurrentJobs: this.maxConcurrentJobs,
      isProcessing: this.isProcessing
    };
  }

  /**
   * Get detailed queue information for admin/monitoring
   */
  getDetailedQueueInfo() {
    const userDetails = [];

    for (const [userId, queue] of this.userQueues.entries()) {
      const activeJobs = this.activeJobsByUser.get(userId) || 0;
      const queuedJobs = queue.map(job => ({
        videoId: job.id,
        addedAt: job.addedAt,
        retries: job.retries
      }));

      userDetails.push({
        userId,
        activeJobs,
        queueLength: queue.length,
        queuedJobs,
        canProcessMore: activeJobs < this.maxUserConcurrentJobs
      });
    }

    return {
      globalLimits: {
        maxGlobalConcurrentJobs: this.maxGlobalConcurrentJobs,
        maxUserConcurrentJobs: this.maxUserConcurrentJobs,
        currentActiveJobs: this.activeJobs
      },
      userDetails,
      lastProcessedUser: this.lastProcessedUser,
      canProcessMore: this.activeJobs < this.maxGlobalConcurrentJobs
    };
  }

  /**
   * Get processing statistics
   */
  async getProcessingStats() {
    try {
      const videos = await Video.getAll();
      const stats = {
        total: videos.length,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        streamingReady: 0
      };

      videos.forEach(video => {
        if (video.processing_status) {
          stats[video.processing_status]++;
        }
        if (video.streaming_ready_path) {
          stats.streamingReady++;
        }
      });

      return stats;
    } catch (error) {
      console.error('[VideoProcessing] Error getting processing stats:', error);
      return null;
    }
  }

  /**
   * Update progress for a video
   */
  updateProgress(videoId, percent, status, message) {
    this.progressStore.set(videoId, {
      percent: Math.min(Math.max(percent, 0), 100), // Clamp between 0-100
      status: status,
      message: message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get progress for a video
   */
  getProgress(videoId) {
    return this.progressStore.get(videoId) || null;
  }

  /**
   * Get all current progress
   */
  getAllProgress() {
    const progress = {};
    for (const [videoId, data] of this.progressStore.entries()) {
      progress[videoId] = data;
    }
    return progress;
  }

  /**
   * Clear progress for a video
   */
  clearProgress(videoId) {
    this.progressStore.delete(videoId);
  }

  /**
   * Update global concurrent job limit
   */
  setGlobalConcurrentJobLimit(limit) {
    if (limit > 0 && limit <= 10) { // Reasonable bounds
      this.maxGlobalConcurrentJobs = limit;
      this.maxConcurrentJobs = limit; // Legacy compatibility
      console.log(`[VideoProcessing] Global concurrent job limit updated to ${limit}`);
      return true;
    }
    return false;
  }

  /**
   * Update per-user concurrent job limit
   */
  setUserConcurrentJobLimit(limit) {
    if (limit > 0 && limit <= 5) { // Reasonable bounds
      this.maxUserConcurrentJobs = limit;
      console.log(`[VideoProcessing] Per-user concurrent job limit updated to ${limit}`);
      return true;
    }
    return false;
  }

  /**
   * Get current configuration
   */
  getConfiguration() {
    return {
      maxGlobalConcurrentJobs: this.maxGlobalConcurrentJobs,
      maxUserConcurrentJobs: this.maxUserConcurrentJobs,
      activeJobs: this.activeJobs,
      totalUsers: this.userQueues.size,
      totalActiveUsers: this.activeJobsByUser.size
    };
  }

  /**
   * Remove user from all queues (for cleanup/admin purposes)
   */
  removeUserFromQueue(userId) {
    let removedJobs = 0;

    // Remove from user queues
    if (this.userQueues.has(userId)) {
      const queue = this.userQueues.get(userId);
      removedJobs = queue.length;

      // Remove from legacy queue as well
      queue.forEach(job => {
        this.processingQueue.delete(job.id);
      });

      this.userQueues.delete(userId);
    }

    // Clean up active job tracking
    this.activeJobsByUser.delete(userId);

    console.log(`[VideoProcessing] Removed user ${userId} from queue (${removedJobs} jobs removed)`);
    return removedJobs;
  }
}

// Create singleton instance
const videoProcessingService = new VideoProcessingService();

module.exports = videoProcessingService;
